package interfaces

import (
	"context"
	"net/http"
	"time"
)

// Target 扫描目标
type Target struct {
	URL        string            `json:"url"`
	Method     string            `json:"method"`
	Headers    map[string]string `json:"headers"`
	Body       string            `json:"body"`
	Parameters map[string]string `json:"parameters"`
}

// ScanResult 扫描结果
type ScanResult struct {
	Target      Target    `json:"target"`
	VulnType    string    `json:"vuln_type"`
	Severity    string    `json:"severity"`
	Confidence  float64   `json:"confidence"`
	Payload     string    `json:"payload"`
	Evidence    string    `json:"evidence"`
	Description string    `json:"description"`
	Timestamp   time.Time `json:"timestamp"`
}

// CrawlerResult 爬虫结果
type CrawlerResult struct {
	URLs       []string          `json:"urls"`
	Forms      []FormInfo        `json:"forms"`
	JSFiles    []string          `json:"js_files"`
	Parameters map[string]string `json:"parameters"`
	Cookies    map[string]string `json:"cookies"`
}

// CrawlResult 新的爬虫结果结构
type CrawlResult struct {
	URLs       []string          `json:"urls"`
	JSFiles    []string          `json:"js_files"`
	Parameters map[string]string `json:"parameters"`
	Forms      []Form            `json:"forms"`
	Cookies    []Cookie          `json:"cookies"`
	Duration   time.Duration     `json:"duration"`
	Source     string            `json:"source"`
}

// Form 表单结构
type Form struct {
	Action string            `json:"action"`
	Method string            `json:"method"`
	Fields map[string]string `json:"fields"`
}

// Cookie Cookie结构
type Cookie struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Domain   string `json:"domain"`
	Path     string `json:"path"`
	Secure   bool   `json:"secure"`
	HttpOnly bool   `json:"httponly"`
}

// FormInfo 表单信息
type FormInfo struct {
	Action     string            `json:"action"`
	Method     string            `json:"method"`
	Fields     map[string]string `json:"fields"`
	Enctype    string            `json:"enctype"`
}

// JSAnalysisResult JavaScript分析结果
type JSAnalysisResult struct {
	URL           string            `json:"url"`
	SensitiveInfo []SensitiveInfo   `json:"sensitive_info"`
	APIEndpoints  []APIEndpoint     `json:"api_endpoints"`
	Variables     map[string]string `json:"variables"`
	Functions     []string          `json:"functions"`
}

// SensitiveInfo 敏感信息
type SensitiveInfo struct {
	Type        string `json:"type"`
	Value       string `json:"value"`
	Description string `json:"description"`
	Line        int    `json:"line"`
}

// APIEndpoint API端点
type APIEndpoint struct {
	URL         string `json:"url"`
	Method      string `json:"method"`
	Description string `json:"description"`
}

// DirScanResult 目录扫描结果
type DirScanResult struct {
	URL        string `json:"url"`
	StatusCode int    `json:"status_code"`
	Size       int64  `json:"size"`
	Title      string `json:"title"`
	Server     string `json:"server"`
}

// ProxyRequest 代理请求
type ProxyRequest struct {
	ID        string              `json:"id"`
	Method    string              `json:"method"`
	URL       string              `json:"url"`
	Headers   map[string][]string `json:"headers"`
	Body      []byte              `json:"body"`
	Timestamp time.Time           `json:"timestamp"`
}

// ProxyResponse 代理响应
type ProxyResponse struct {
	ID         string              `json:"id"`
	StatusCode int                 `json:"status_code"`
	Headers    map[string][]string `json:"headers"`
	Body       []byte              `json:"body"`
	Size       int64               `json:"size"`
	Duration   time.Duration       `json:"duration"`
}

// Crawler 爬虫接口
type Crawler interface {
	// Crawl 执行爬虫
	Crawl(ctx context.Context, target string) (*CrawlResult, error)

	// SetConfig 设置配置
	SetConfig(config interface{}) error

	// GetName 获取爬虫名称
	GetName() string
}

// Scanner 扫描器接口
type Scanner interface {
	// Scan 执行扫描
	Scan(ctx context.Context, target Target) ([]ScanResult, error)
	
	// SetConfig 设置配置
	SetConfig(config interface{}) error
	
	// GetName 获取扫描器名称
	GetName() string
}

// XSSScanner XSS扫描器接口
type XSSScanner interface {
	Scanner
	
	// TestPayload 测试单个payload
	TestPayload(ctx context.Context, target Target, payload string) (*ScanResult, error)
	
	// GetPayloads 获取payload列表
	GetPayloads(context string) []string
	
	// AnalyzeContext 分析上下文
	AnalyzeContext(response string) string
}

// DirScanner 目录扫描器接口
type DirScanner interface {
	// ScanDirectory 扫描目录
	ScanDirectory(ctx context.Context, baseURL string, wordlist []string) ([]DirScanResult, error)
	
	// SetConfig 设置配置
	SetConfig(config interface{}) error
	
	// GetName 获取扫描器名称
	GetName() string
}

// JSAnalyzer JavaScript分析器接口
type JSAnalyzer interface {
	// AnalyzeJS 分析JavaScript文件
	AnalyzeJS(ctx context.Context, jsURL string, content string) (*JSAnalysisResult, error)
	
	// ExtractSensitiveInfo 提取敏感信息
	ExtractSensitiveInfo(content string) []SensitiveInfo
	
	// ExtractAPIEndpoints 提取API端点
	ExtractAPIEndpoints(content string) []APIEndpoint
	
	// SetConfig 设置配置
	SetConfig(config interface{}) error
}

// Reporter 报告生成器接口
type Reporter interface {
	// GenerateReport 生成报告
	GenerateReport(results []ScanResult, format string) (string, error)
	
	// SaveReport 保存报告
	SaveReport(content string, filename string) error
	
	// GetSupportedFormats 获取支持的格式
	GetSupportedFormats() []string
}

// ProxyServer 代理服务器接口
type ProxyServer interface {
	// Start 启动代理服务器
	Start(ctx context.Context, host string, port int) error
	
	// Stop 停止代理服务器
	Stop() error
	
	// SetRequestHandler 设置请求处理器
	SetRequestHandler(handler func(*ProxyRequest))
	
	// SetResponseHandler 设置响应处理器
	SetResponseHandler(handler func(*ProxyResponse))
}

// HTTPClient HTTP客户端接口
type HTTPClient interface {
	// Get 发送GET请求
	Get(ctx context.Context, url string, headers map[string]string) (*http.Response, error)
	
	// Post 发送POST请求
	Post(ctx context.Context, url string, headers map[string]string, body string) (*http.Response, error)
	
	// Request 发送自定义请求
	Request(ctx context.Context, method, url string, headers map[string]string, body string) (*http.Response, error)
	
	// SetTimeout 设置超时时间
	SetTimeout(timeout time.Duration)
	
	// SetProxy 设置代理
	SetProxy(proxyURL string) error
}

// Cache 缓存接口
type Cache interface {
	// Set 设置缓存
	Set(key string, value interface{}, expiration time.Duration) error
	
	// Get 获取缓存
	Get(key string) (interface{}, bool)
	
	// Delete 删除缓存
	Delete(key string) error
	
	// Clear 清空缓存
	Clear() error
	
	// Exists 检查是否存在
	Exists(key string) bool
}

// ProgressReporter 进度报告器接口
type ProgressReporter interface {
	// Start 开始进度报告
	Start(total int, description string)
	
	// Update 更新进度
	Update(current int, message string)
	
	// Finish 完成进度报告
	Finish(message string)
	
	// Error 报告错误
	Error(err error)
}

// RateLimiter 速率限制器接口
type RateLimiter interface {
	// Wait 等待
	Wait(ctx context.Context) error
	
	// Allow 检查是否允许
	Allow() bool
	
	// SetRate 设置速率
	SetRate(rate float64)
}

// WordlistManager 字典管理器接口
type WordlistManager interface {
	// LoadWordlist 加载字典
	LoadWordlist(filename string) ([]string, error)
	
	// GetWordlistByTech 根据技术栈获取字典
	GetWordlistByTech(tech string) ([]string, error)
	
	// GetDefaultWordlist 获取默认字典
	GetDefaultWordlist() ([]string, error)
	
	// MergeWordlists 合并字典
	MergeWordlists(wordlists ...[]string) []string
}
