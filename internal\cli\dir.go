package cli

import (
	"context"
	"fmt"
	"strings"

	"github.com/spf13/cobra"
	"Pulsex/internal/logger"
)

var (
	dirURL       string
	dirFile      string
	dirExtension string
	dirThreads   int
	dirWordlist  string
)

// dirCmd 独立执行目录遍历任务
var dirCmd = &cobra.Command{
	Use:   "dir",
	Short: "独立执行目录遍历任务",
	Long: `独立执行目录遍历任务

专门用于发现Web应用中的隐藏目录和文件。
支持智能字典选择和技术栈识别。

核心特性:
  • 智能字典选择: 根据技术栈自动选择合适的字典
  • 技术栈识别: 自动识别目标使用的技术栈
  • 多线程扫描: 高效的并发扫描
  • 状态码分析: 智能分析HTTP响应状态
  • 内容长度检测: 过滤无效响应

支持的技术栈:
  • php: PHP应用专用字典
  • asp/aspx: ASP.NET应用字典  
  • jsp: Java Web应用字典
  • js: JavaScript/Node.js字典
  • all: 通用字典(默认)

示例:
  pulsex dir -u https://example.com -e php
  pulsex dir -f targets.txt -e all
  pulsex dir -u https://example.com -e php -t 20`,
	RunE: func(cmd *cobra.Command, args []string) error {
		config := getConfig()
		if config == nil {
			return fmt.Errorf("配置未初始化")
		}

		// 检查目录扫描模块是否启用
		if !config.DirScan.Enable {
			return fmt.Errorf("目录扫描模块未启用，请检查配置文件")
		}

		// 验证参数
		if dirURL == "" && dirFile == "" {
			return fmt.Errorf("必须指定 -u (URL) 或 -f (文件)")
		}

		if dirURL != "" && dirFile != "" {
			return fmt.Errorf("不能同时指定 -u 和 -f 参数")
		}

		// 验证扩展名参数
		validExtensions := []string{"php", "asp", "aspx", "jsp", "js", "html", "all"}
		if dirExtension != "" && !contains(validExtensions, dirExtension) {
			return fmt.Errorf("不支持的扩展名: %s，支持的扩展名: %s", 
				dirExtension, strings.Join(validExtensions, ", "))
		}

		// 打印模块状态
		logger.PrintModuleStatus("dir", config.DirScan.Enable)
		fmt.Printf("[%s] [%s] 语言类型: %s\n", 
			logger.Blue("DIR-BRUTE"), 
			logger.Green("INFO"), 
			dirExtension)
		fmt.Println("-----------------------------------------------------------")

		// 获取目标列表
		var targets []string
		if dirURL != "" {
			targets = []string{dirURL}
		} else {
			var err error
			targets, err = readTargetsFromFile(dirFile)
			if err != nil {
				return fmt.Errorf("读取目标文件失败: %v", err)
			}
		}

		if len(targets) == 0 {
			return fmt.Errorf("没有找到有效的目标")
		}

		logger.Infof("开始目录遍历扫描，目标数量: %d", len(targets))

		// 创建上下文
		ctx := context.Background()

		// 执行目录扫描
		totalFound := 0
		for i, target := range targets {
			logger.PrintProgress(i+1, len(targets), fmt.Sprintf("目录扫描: %s", target))
			
			found, err := scanDirectoryTarget(ctx, target, dirExtension, config)
			if err != nil {
				logger.Errorf("扫描目标 %s 失败: %v", target, err)
				continue
			}
			
			totalFound += found
		}

		if totalFound > 0 {
			logger.Success(fmt.Sprintf("目录扫描完成，发现 %d 个有效路径", totalFound))
		} else {
			logger.Success("目录扫描完成，未发现有效路径")
		}

		return nil
	},
}

func init() {
	dirCmd.Flags().StringVarP(&dirURL, "url", "u", "", "目标URL")
	dirCmd.Flags().StringVarP(&dirFile, "file", "f", "", "目标文件路径")
	dirCmd.Flags().StringVarP(&dirExtension, "extension", "e", "all", "指定语言类型 (php|asp|aspx|jsp|js|html|all)")
	dirCmd.Flags().IntVarP(&dirThreads, "threads", "t", 20, "并发线程数")
	dirCmd.Flags().StringVar(&dirWordlist, "wordlist", "", "自定义字典文件路径")
}

// scanDirectoryTarget 扫描单个目录目标
func scanDirectoryTarget(ctx context.Context, target, extension string, config interface{}) (int, error) {
	logger.Infof("开始目录扫描: %s", target)
	logger.Infof("技术栈: %s", extension)
	
	// TODO: 实现具体的目录扫描逻辑
	// 1. 根据技术栈选择字典
	// 2. 加载字典文件
	// 3. 生成扫描URL
	// 4. 并发发送请求
	// 5. 分析响应状态码和内容
	// 6. 过滤有效结果
	// 7. 生成扫描报告
	
	foundCount := 0
	
	// 模拟扫描过程
	if target != "" {
		// 这里应该是实际的目录扫描逻辑
		logger.Infof("目录扫描完成: %s", target)
	}
	
	return foundCount, nil
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}
