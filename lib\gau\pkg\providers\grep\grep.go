package grep

import (
	"context"
	"fmt"
	"net/url"
	"regexp"
	"strings"
	"time"

	"github.com/lc/gau/v2/pkg/httpclient"
	"github.com/lc/gau/v2/pkg/providers"
	"github.com/sirupsen/logrus"
)

const (
	Name = "grep"
)

// verify interface compliance
var _ providers.Provider = (*Client)(nil)

// Client is the structure that holds the configuration
type Client struct {
	config *providers.Config
}

// SearchEngine 搜索引擎配置
type SearchEngine struct {
	Name    string
	BaseURL string
	Query   string
}

// 支持的数据源列表 - 使用公开API和数据源
var searchEngines = []SearchEngine{
	{
		Name:    "GitHub",
		BaseURL: "https://api.github.com/search/code",
		Query:   "q",
	},
}

func New(c *providers.Config) *Client {
	return &Client{config: c}
}

func (c *Client) Name() string {
	return Name
}

// Fetch 从搜索引擎获取包含目标域名的URL
func (c *Client) Fetch(ctx context.Context, domain string, results chan string) error {
	logrus.WithFields(logrus.Fields{"provider": Name}).Debugf("开始搜索域名: %s", domain)

	// 构建搜索查询
	queries := c.buildSearchQueries(domain)

	pageCount := 0
	for _, query := range queries {
		for _, engine := range searchEngines {
			select {
			case <-ctx.Done():
				return nil
			default:
				pageCount++
				logrus.WithFields(logrus.Fields{"provider": Name, "page": pageCount}).Infof("fetching %s", domain)

				if err := c.searchEngine(engine, query, results); err != nil {
					logrus.WithFields(logrus.Fields{"provider": Name}).Warnf("%s搜索失败: %v", engine.Name, err)
					continue // 继续下一个搜索引擎
				}

				// 添加短暂延迟避免被封
				time.Sleep(500 * time.Millisecond)
			}
		}
	}

	return nil
}

// buildSearchQueries 构建搜索查询 - 简化版本
func (c *Client) buildSearchQueries(domain string) []string {
	queries := []string{
		// 基本域名搜索
		fmt.Sprintf(`"%s"`, domain),
		// URL模式搜索
		fmt.Sprintf(`"http://%s" OR "https://%s"`, domain, domain),
		// 子域名和API搜索
		fmt.Sprintf(`"*.%s" OR "%s/api" OR "%s/admin"`, domain, domain, domain),
	}

	return queries
}

// searchEngine 执行数据源搜索
func (c *Client) searchEngine(engine SearchEngine, query string, results chan string) error {
	if engine.Name == "GitHub" {
		return c.searchGitHub(query, results)
	}

	// 构建搜索URL
	searchURL := fmt.Sprintf("%s?%s=%s", engine.BaseURL, engine.Query, url.QueryEscape(query))

	// 发送请求
	resp, err := httpclient.MakeRequest(c.config.Client, searchURL, 2, 10)
	if err != nil {
		return fmt.Errorf("%s搜索请求失败: %v", engine.Name, err)
	}

	// 从HTML响应中提取URL
	urls := c.extractURLsFromHTML(string(resp))
	for _, extractedURL := range urls {
		results <- extractedURL
	}

	return nil
}

// searchGitHub 搜索GitHub代码
func (c *Client) searchGitHub(query string, results chan string) error {
	// GitHub API搜索URL
	searchURL := fmt.Sprintf("https://api.github.com/search/code?q=%s&per_page=30", url.QueryEscape(query))

	// 发送请求
	resp, err := httpclient.MakeRequest(c.config.Client, searchURL, 2, 10)
	if err != nil {
		return fmt.Errorf("GitHub搜索请求失败: %v", err)
	}

	// 解析GitHub API响应
	urls := c.extractURLsFromGitHubAPI(string(resp))
	for _, extractedURL := range urls {
		results <- extractedURL
	}

	return nil
}

// extractURLsFromHTML 从HTML响应中提取URL
func (c *Client) extractURLsFromHTML(html string) []string {
	var urls []string

	// 完整URL正则表达式
	urlRegex := regexp.MustCompile(`https?://[^\s"'<>()]+`)
	matches := urlRegex.FindAllString(html, -1)
	urls = append(urls, matches...)

	// 相对路径正则表达式
	pathRegex := regexp.MustCompile(`/[a-zA-Z0-9._/-]+\.[a-zA-Z0-9]+`)
	pathMatches := pathRegex.FindAllString(html, -1)
	urls = append(urls, pathMatches...)

	// API端点正则表达式
	apiRegex := regexp.MustCompile(`/api/[a-zA-Z0-9._/-]+`)
	apiMatches := apiRegex.FindAllString(html, -1)
	urls = append(urls, apiMatches...)

	// 管理端点正则表达式
	adminRegex := regexp.MustCompile(`/admin/[a-zA-Z0-9._/-]*`)
	adminMatches := adminRegex.FindAllString(html, -1)
	urls = append(urls, adminMatches...)

	// 配置文件路径正则表达式
	configRegex := regexp.MustCompile(`/[a-zA-Z0-9._/-]*\.(json|xml|config|env|yml|yaml)`)
	configMatches := configRegex.FindAllString(html, -1)
	urls = append(urls, configMatches...)

	// 去重并过滤
	uniqueURLs := make(map[string]bool)
	var filteredURLs []string

	for _, url := range urls {
		// 清理URL
		url = strings.TrimSpace(url)
		url = strings.Trim(url, "\"'<>()[]")

		// 过滤无效URL
		if len(url) < 4 || uniqueURLs[url] {
			continue
		}

		// 过滤明显的垃圾URL
		if strings.Contains(url, "javascript:") ||
		   strings.Contains(url, "mailto:") ||
		   strings.Contains(url, "tel:") ||
		   strings.HasSuffix(url, ".css") ||
		   strings.HasSuffix(url, ".js") ||
		   strings.HasSuffix(url, ".png") ||
		   strings.HasSuffix(url, ".jpg") ||
		   strings.HasSuffix(url, ".gif") {
			continue
		}

		uniqueURLs[url] = true
		filteredURLs = append(filteredURLs, url)
	}

	return filteredURLs
}

// extractURLsFromGitHubAPI 从GitHub API响应中提取URL
func (c *Client) extractURLsFromGitHubAPI(jsonResp string) []string {
	var urls []string

	// 简单的正则表达式提取URL，避免复杂的JSON解析
	// 完整URL正则表达式
	urlRegex := regexp.MustCompile(`https?://[^\s"'<>()]+`)
	matches := urlRegex.FindAllString(jsonResp, -1)
	urls = append(urls, matches...)

	// 相对路径正则表达式
	pathRegex := regexp.MustCompile(`/[a-zA-Z0-9._/-]+\.[a-zA-Z0-9]+`)
	pathMatches := pathRegex.FindAllString(jsonResp, -1)
	urls = append(urls, pathMatches...)

	// 去重并过滤
	uniqueURLs := make(map[string]bool)
	var filteredURLs []string

	for _, url := range urls {
		// 清理URL
		url = strings.TrimSpace(url)
		url = strings.Trim(url, "\"'<>()[]")

		// 过滤无效URL
		if len(url) < 4 || uniqueURLs[url] {
			continue
		}

		// 过滤GitHub自身的URL
		if strings.Contains(url, "github.com") ||
		   strings.Contains(url, "api.github.com") {
			continue
		}

		uniqueURLs[url] = true
		filteredURLs = append(filteredURLs, url)
	}

	return filteredURLs
}
