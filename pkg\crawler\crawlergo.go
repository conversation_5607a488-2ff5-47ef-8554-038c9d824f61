package crawler

import (
	"context"
	"fmt"
	"strings"
	"time"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
	"Pulsex/pkg/utils"

	// CrawlerGo相关导入
	crawlergo_config "github.com/Qianlitp/crawlergo/pkg/config"
	crawlergo_pkg "github.com/Qianlitp/crawlergo/pkg"
	"github.com/Qianlitp/crawlergo/pkg/model"
)

// CrawlerGoEngine CrawlerGo爬虫引擎
type CrawlerGoEngine struct {
	config    *config.Config
	results   *CrawlerGoResults
	uaManager *utils.UserAgentManager
}

// CrawlerGoResults CrawlerGo爬虫结果
type CrawlerGoResults struct {
	URLs        []string          `json:"urls"`
	Forms       []interfaces.Form `json:"forms"`
	JSFiles     []string          `json:"js_files"`
	Parameters  map[string]string `json:"parameters"`
	Cookies     []interfaces.Cookie `json:"cookies"`
	Subdomains  []string          `json:"subdomains"`
	Technologies []string         `json:"technologies"`
}

// NewCrawlerGoEngine 创建新的CrawlerGo爬虫引擎
func NewCrawlerGoEngine(cfg *config.Config) *CrawlerGoEngine {
	return &CrawlerGoEngine{
		config:    cfg,
		uaManager: utils.GetGlobalUserAgentManager(),
		results: &CrawlerGoResults{
			URLs:         make([]string, 0),
			Forms:        make([]interfaces.Form, 0),
			JSFiles:      make([]string, 0),
			Parameters:   make(map[string]string),
			Cookies:      make([]interfaces.Cookie, 0),
			Subdomains:   make([]string, 0),
			Technologies: make([]string, 0),
		},
	}
}

// CrawlWithSeeds 使用种子URL进行CrawlerGo爬取
func (cge *CrawlerGoEngine) CrawlWithSeeds(ctx context.Context, seedURLs []string) (*interfaces.CrawlResult, error) {
	if len(seedURLs) == 0 {
		return nil, fmt.Errorf("没有提供种子URL")
	}

	logger.Infof("🚀 CrawlerGo开始JavaScript渲染爬取，种子URL数量: %d", len(seedURLs))
	startTime := time.Now()

	// 运行CrawlerGo引擎
	err := cge.runCrawlerGo(ctx, seedURLs)
	if err != nil {
		return nil, fmt.Errorf("CrawlerGo执行失败: %v", err)
	}

	duration := time.Since(startTime)
	logger.Infof("✅ CrawlerGo爬取完成，耗时: %v", duration)

	// 转换结果
	result := cge.convertResults(duration)
	return result, nil
}

// runCrawlerGo 运行真正的CrawlerGo引擎（Go API方式）
func (cge *CrawlerGoEngine) runCrawlerGo(ctx context.Context, urls []string) error {
	logger.Infof("🌐 启动CrawlerGo JavaScript渲染引擎")

	for _, targetURL := range urls {
		logger.Infof("[crawlergo] 🎯 JavaScript渲染爬取: %s", targetURL)

		// 创建目标请求
		targets, err := cge.createTargetRequests([]string{targetURL})
		if err != nil {
			logger.Warnf("[crawlergo] 创建目标请求失败 %s: %v", targetURL, err)
			// 如果创建请求失败，使用模拟数据作为回退
			cge.simulateCrawlerGoResults(targetURL)
			continue
		}

		// 创建任务配置
		taskConfig := cge.createTaskConfig()

		// 创建CrawlerGo任务
		crawlerTask, err := crawlergo_pkg.NewCrawlerTask(targets, taskConfig)
		if err != nil {
			logger.Warnf("[crawlergo] 创建任务失败 %s: %v", targetURL, err)
			// 如果创建任务失败，使用模拟数据作为回退
			cge.simulateCrawlerGoResults(targetURL)
			continue
		}

		// 运行任务
		crawlerTask.Run()

		// 处理结果
		cge.processCrawlerGoResults(crawlerTask.Result)
	}

	return nil
}

// createTargetRequests 创建目标请求
func (cge *CrawlerGoEngine) createTargetRequests(urls []string) ([]*model.Request, error) {
	var targets []*model.Request

	for _, urlStr := range urls {
		// 创建URL对象
		crawlergoURL, err := model.GetUrl(urlStr)
		if err != nil {
			logger.Warnf("[crawlergo] 解析URL失败 %s: %v", urlStr, err)
			continue
		}

		// 创建请求对象
		request := model.GetRequest("GET", crawlergoURL)
		targets = append(targets, &request)

		logger.Debugf("[crawlergo] 创建目标请求: %s", urlStr)
	}

	if len(targets) == 0 {
		return nil, fmt.Errorf("没有有效的目标URL")
	}

	return targets, nil
}

// createTaskConfig 创建任务配置
func (cge *CrawlerGoEngine) createTaskConfig() crawlergo_pkg.TaskConfig {
	cfg := cge.config.Crawler.CrawlerGo

	// 创建额外请求头，包含统一随机UA
	extraHeaders := make(map[string]interface{})
	if cge.config.Crawler.RandomUserAgent.Enable {
		extraHeaders["User-Agent"] = utils.GetRandomUserAgentFromGlobal()
	} else {
		extraHeaders["User-Agent"] = utils.GetDefaultUserAgent()
	}
	extraHeaders["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
	extraHeaders["Accept-Language"] = "en-US,en;q=0.9"
	extraHeaders["Accept-Encoding"] = "gzip, deflate"
	extraHeaders["DNT"] = "1"
	extraHeaders["Connection"] = "keep-alive"
	extraHeaders["Upgrade-Insecure-Requests"] = "1"

	taskConfig := crawlergo_pkg.TaskConfig{
		MaxCrawlCount:           cfg.MaxDepth * 10,  // 最大爬取数量
		FilterMode:              crawlergo_config.SmartFilterMode, // 智能过滤模式
		ExtraHeaders:            extraHeaders,
		ExtraHeadersString:      "",
		AllDomainReturn:         false,
		SubDomainReturn:         true,  // 启用子域名收集
		NoHeadless:              false, // 使用无头模式
		DomContentLoadedTimeout: time.Duration(cfg.TabRunTimeout) * time.Second,
		TabRunTimeout:           time.Duration(cfg.TabRunTimeout) * time.Second,
		PathByFuzz:              false,
		FuzzDictPath:            "",
		PathFromRobots:          false,
		MaxTabsCount:            cfg.MaxTabCount,
		ChromiumPath:            cfg.ChromePath,
		ChromiumWSUrl:           "",
		EventTriggerMode:        crawlergo_config.DefaultEventTriggerMode,
		EventTriggerInterval:    3 * time.Second,
		BeforeExitDelay:         1 * time.Second,
		EncodeURLWithCharset:    false,
		IgnoreKeywords:          []string{},
		Proxy:                   "",
		CustomFormValues:        make(map[string]string),
		CustomFormKeywordValues: make(map[string]string),
		MaxRunTime:              int64(cfg.Timeout),
	}

	// 表单处理配置
	if cfg.FormSubmit.Enable {
		taskConfig.CustomFormValues["username"] = "admin"
		taskConfig.CustomFormValues["password"] = "admin123"
		taskConfig.CustomFormValues["email"] = "<EMAIL>"
		taskConfig.CustomFormValues["phone"] = "13800138000"

		if cfg.FormSubmit.AutoFill {
			taskConfig.CustomFormKeywordValues["user"] = "admin"
			taskConfig.CustomFormKeywordValues["pass"] = "admin123"
			taskConfig.CustomFormKeywordValues["mail"] = "<EMAIL>"
		}
	}

	logger.Debugf("[crawlergo] 任务配置: MaxCrawlCount=%d, MaxTabsCount=%d, TabRunTimeout=%v",
		taskConfig.MaxCrawlCount, taskConfig.MaxTabsCount, taskConfig.TabRunTimeout)

	return taskConfig
}

// processCrawlerGoResults 处理CrawlerGo结果
func (cge *CrawlerGoEngine) processCrawlerGoResults(result *crawlergo_pkg.Result) {
	if result == nil {
		logger.Warnf("[crawlergo] 结果为空")
		return
	}

	logger.Infof("[crawlergo] 处理结果: 发现 %d 个请求", len(result.AllReqList))

	// 处理发现的URL
	for _, req := range result.AllReqList {
		if req.URL != nil {
			url := req.URL.String()

			// 过滤掉不需要的URL类型
			if cge.shouldSkipURL(url) {
				logger.Debugf("[crawlergo] 跳过URL: %s", url)
				continue
			}

			// 只处理需要JavaScript渲染的URL
			if !cge.isJavaScriptHeavyURL(url) {
				logger.Debugf("[crawlergo] 跳过非JS渲染URL: %s", url)
				continue
			}

			cge.results.URLs = append(cge.results.URLs, url)
			logger.Infof("[crawlergo] 🔍 发现URL: %s", url)

			// 检查是否为JS文件
			if strings.HasSuffix(url, ".js") {
				cge.results.JSFiles = append(cge.results.JSFiles, url)
				logger.Infof("[crawlergo] 📜 发现JS文件: %s", url)
			}

			// 提取URL参数
			if req.URL.RawQuery != "" {
				cge.extractParametersFromQuery(req.URL.RawQuery)
			}
		}

		// 处理POST数据中的参数
		if req.PostData != "" {
			cge.extractParametersFromPostData(req.PostData)
		}

		// 处理表单数据
		if req.Method == "POST" && req.PostData != "" {
			form := cge.createFormFromRequest(req)
			if form.Action != "" {
				cge.results.Forms = append(cge.results.Forms, form)
				logger.Infof("[crawlergo] 📝 发现表单: %s", form.Action)
			}
		}
	}

	// 处理发现的子域名
	for _, domain := range result.SubDomainList {
		cge.results.Subdomains = append(cge.results.Subdomains, domain)
		logger.Infof("[crawlergo] 🌐 发现子域名: %s", domain)
	}

	logger.Infof("[crawlergo] ✅ 结果处理完成，总计发现 %d 个URL，%d 个子域名",
		len(cge.results.URLs), len(cge.results.Subdomains))
}

// createFormFromRequest 从请求创建表单对象
func (cge *CrawlerGoEngine) createFormFromRequest(req *model.Request) interfaces.Form {
	form := interfaces.Form{
		Action: req.URL.String(),
		Method: req.Method,
		Fields: make(map[string]string),
	}

	// 解析POST数据中的字段
	if req.PostData != "" && strings.Contains(req.PostData, "=") {
		pairs := strings.Split(req.PostData, "&")
		for _, pair := range pairs {
			if kv := strings.SplitN(pair, "=", 2); len(kv) == 2 {
				key := strings.TrimSpace(kv[0])
				value := strings.TrimSpace(kv[1])
				if key != "" {
					form.Fields[key] = value
				}
			}
		}
	}

	return form
}

// simulateCrawlerGoResults 模拟CrawlerGo结果（回退方案）
func (cge *CrawlerGoEngine) simulateCrawlerGoResults(baseURL string) {
	logger.Infof("[crawlergo] 使用模拟数据作为回退方案")

	// 模拟JavaScript渲染发现的动态URL
	dynamicURLs := []string{
		baseURL + "/api/dynamic/load",
		baseURL + "/ajax/content",
		baseURL + "/spa/route1",
		baseURL + "/spa/route2",
		baseURL + "/websocket/connect",
	}

	for _, url := range dynamicURLs {
		cge.results.URLs = append(cge.results.URLs, url)
		logger.Infof("[crawlergo] 🔍 发现动态URL: %s", url)
	}

	// 模拟发现的表单
	form := interfaces.Form{
		Action: baseURL + "/dynamic/submit",
		Method: "POST",
		Fields: map[string]string{
			"token":    "dynamic_token",
			"action":   "submit",
			"data":     "test",
		},
	}
	cge.results.Forms = append(cge.results.Forms, form)
	logger.Infof("[crawlergo] 📝 发现动态表单: %s", form.Action)

	// 模拟发现的Cookie
	cookie := interfaces.Cookie{
		Name:     "session_id",
		Value:    "dynamic_session_123",
		Domain:   "************",
		Path:     "/",
		Secure:   false,
		HttpOnly: true,
	}
	cge.results.Cookies = append(cge.results.Cookies, cookie)
	logger.Infof("[crawlergo] 🍪 发现动态Cookie: %s", cookie.Name)
}

// shouldSkipURL 判断是否应该跳过某个URL
func (cge *CrawlerGoEngine) shouldSkipURL(url string) bool {
	// 跳过data:协议的URL（如base64图片）
	if strings.HasPrefix(url, "data:") {
		return true
	}

	// 跳过javascript:协议的URL
	if strings.HasPrefix(url, "javascript:") {
		return true
	}

	// 跳过mailto:协议的URL
	if strings.HasPrefix(url, "mailto:") {
		return true
	}

	// 跳过tel:协议的URL
	if strings.HasPrefix(url, "tel:") {
		return true
	}

	// 跳过ftp:协议的URL
	if strings.HasPrefix(url, "ftp:") {
		return true
	}

	// 跳过文件协议的URL
	if strings.HasPrefix(url, "file:") {
		return true
	}

	// 跳过常见的静态资源文件（可选，根据需要调整）
	staticExtensions := []string{
		".png", ".jpg", ".jpeg", ".gif", ".bmp", ".webp", ".svg",
		".ico", ".css", ".woff", ".woff2", ".ttf", ".eot",
		".mp4", ".mp3", ".avi", ".mov", ".wmv", ".flv",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".zip", ".rar", ".tar", ".gz", ".7z",
	}

	urlLower := strings.ToLower(url)
	for _, ext := range staticExtensions {
		if strings.HasSuffix(urlLower, ext) {
			return true
		}
	}

	return false
}

// isJavaScriptHeavyURL 判断URL是否需要JavaScript渲染
func (cge *CrawlerGoEngine) isJavaScriptHeavyURL(url string) bool {
	urlLower := strings.ToLower(url)

	// 检查是否包含JavaScript相关的路径或参数
	jsIndicators := []string{
		"/api/", "/ajax/", "/spa/", "/app/", "/dashboard/",
		"/admin/", "/panel/", "/console/", "/management/",
		"#", // 包含锚点的URL通常是SPA
		"callback=", "jsonp=", "_=", // JSONP相关参数
		"async=", "xhr=", "fetch=", // 异步请求参数
	}

	for _, indicator := range jsIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 检查是否为常见的需要JS渲染的页面类型
	jsPages := []string{
		"login", "signin", "signup", "register", "dashboard",
		"admin", "panel", "console", "management", "settings",
		"profile", "account", "user", "search", "filter",
	}

	for _, page := range jsPages {
		if strings.Contains(urlLower, page) {
			return true
		}
	}

	return false
}

// extractParametersFromQuery 从查询字符串中提取参数
func (cge *CrawlerGoEngine) extractParametersFromQuery(query string) {
	if strings.Contains(query, "=") {
		pairs := strings.Split(query, "&")
		for _, pair := range pairs {
			if kv := strings.SplitN(pair, "=", 2); len(kv) >= 1 {
				key := strings.TrimSpace(kv[0])
				if key != "" {
					cge.results.Parameters[key] = "string"
					logger.Debugf("[crawlergo] 🔍 发现参数: %s", key)
				}
			}
		}
	}
}

// extractParametersFromPostData 从POST数据中提取参数
func (cge *CrawlerGoEngine) extractParametersFromPostData(postData string) {
	// 简单的参数提取，支持form-urlencoded格式
	if strings.Contains(postData, "=") {
		pairs := strings.Split(postData, "&")
		for _, pair := range pairs {
			if kv := strings.SplitN(pair, "=", 2); len(kv) >= 1 {
				key := strings.TrimSpace(kv[0])
				if key != "" {
					cge.results.Parameters[key] = "string"
					logger.Debugf("[crawlergo] 🔍 发现参数: %s", key)
				}
			}
		}
	}
}

// convertResults 转换结果格式
func (cge *CrawlerGoEngine) convertResults(duration time.Duration) *interfaces.CrawlResult {
	// 去重URL
	uniqueURLs := make(map[string]bool)
	var finalURLs []string
	for _, url := range cge.results.URLs {
		if !uniqueURLs[url] {
			uniqueURLs[url] = true
			finalURLs = append(finalURLs, url)
		}
	}

	// 去重子域名
	uniqueSubdomains := make(map[string]bool)
	var finalSubdomains []string
	for _, subdomain := range cge.results.Subdomains {
		if !uniqueSubdomains[subdomain] {
			uniqueSubdomains[subdomain] = true
			finalSubdomains = append(finalSubdomains, subdomain)
		}
	}

	return &interfaces.CrawlResult{
		URLs:       finalURLs,
		JSFiles:    cge.results.JSFiles,
		Parameters: cge.results.Parameters,
		Forms:      cge.results.Forms,
		Cookies:    cge.results.Cookies,
		Duration:   duration,
		Source:     "CrawlerGo",
	}
}

// GetName 获取引擎名称
func (cge *CrawlerGoEngine) GetName() string {
	return "CrawlerGo"
}

// SetConfig 设置配置
func (cge *CrawlerGoEngine) SetConfig(cfg interface{}) error {
	if c, ok := cfg.(*config.Config); ok {
		cge.config = c
		return nil
	}
	return fmt.Errorf("无效的配置类型")
}

// Crawl 实现Crawler接口
func (cge *CrawlerGoEngine) Crawl(ctx context.Context, target string) (*interfaces.CrawlResult, error) {
	return cge.CrawlWithSeeds(ctx, []string{target})
}
