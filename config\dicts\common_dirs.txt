# 通用目录字典
admin
administrator
login
panel
dashboard
config
backup
test
demo
dev
staging
api
v1
v2
uploads
files
images
css
js
assets
static
public
private
secure
hidden
secret
temp
tmp
cache
logs
log
data
db
database
sql
mysql
oracle
postgres
mongodb
redis
elasticsearch
kibana
grafana
jenkins
gitlab
github
bitbucket
svn
git
.git
.svn
.hg
.bzr
phpmyadmin
adminer
wp-admin
wp-content
wp-includes
drupal
joomla
magento
prestashop
opencart
laravel
symfony
codeigniter
cakephp
zend
yii
rails
django
flask
express
spring
struts
hibernate
mybatis
maven
gradle
npm
composer
bower
webpack
gulp
grunt
node_modules
vendor
lib
libs
library
libraries
include
includes
require
requires
common
shared
utils
utilities
helpers
models
views
controllers
middleware
routes
handlers
services
components
modules
plugins
extensions
addons
themes
templates
layouts
partials
fragments
snippets
widgets
blocks
elements
forms
fields
inputs
outputs
reports
analytics
statistics
metrics
monitoring
health
status
info
about
contact
help
support
faq
docs
documentation
manual
guide
tutorial
examples
samples
demos
tests
specs
benchmarks
performance
security
audit
compliance
privacy
terms
legal
license
copyright
credits
acknowledgments
changelog
history
version
release
build
deploy
deployment
production
staging
development
testing
qa
uat
sandbox
playground
lab
workshop
training
education
learning
courses
lessons
exercises
assignments
projects
portfolio
gallery
showcase
blog
news
articles
posts
pages
content
media
downloads
resources
tools
utilities
scripts
programs
applications
software
firmware
drivers
patches
updates
upgrades
migrations
backups
archives
exports
imports
sync
synchronization
replication
clustering
load-balancing
proxy
gateway
firewall
vpn
ssl
tls
certificates
keys
tokens
sessions
cookies
cache
storage
filesystem
directory
folder
file
document
pdf
doc
docx
xls
xlsx
ppt
pptx
txt
csv
xml
json
yaml
yml
ini
conf
cfg
properties
settings
preferences
options
parameters
variables
constants
definitions
declarations
specifications
schemas
models
entities
objects
classes
interfaces
abstracts
traits
mixins
decorators
annotations
attributes
metadata
tags
labels
categories
groups
collections
lists
arrays
maps
dictionaries
sets
queues
stacks
trees
graphs
networks
connections
relationships
associations
dependencies
references
links
urls
uris
paths
routes
endpoints
apis
services
microservices
functions
methods
procedures
operations
actions
commands
queries
requests
responses
messages
events
notifications
alerts
warnings
errors
exceptions
logs
traces
debugging
profiling
monitoring
observability
telemetry
instrumentation
measurement
analysis
reporting
visualization
dashboards
charts
graphs
tables
grids
forms
dialogs
modals
popups
tooltips
menus
navigation
breadcrumbs
pagination
search
filters
sorting
grouping
aggregation
summarization
calculation
computation
processing
transformation
validation
verification
authentication
authorization
permissions
roles
users
accounts
profiles
settings
preferences
configurations
customizations
personalizations
localizations
internationalizations
translations
languages
regions
timezones
currencies
formats
standards
protocols
specifications
implementations
integrations
connectors
adapters
bridges
wrappers
facades
proxies
decorators
interceptors
filters
middleware
plugins
extensions
modules
components
widgets
controls
elements
templates
layouts
themes
skins
styles
stylesheets
scripts
libraries
frameworks
platforms
environments
containers
images
volumes
networks
clusters
nodes
pods
services
deployments
configurations
secrets
configmaps
ingresses
routes
policies
rules
constraints
limits
quotas
resources
capacities
scaling
autoscaling
load-balancing
health-checks
readiness
liveness
startup
shutdown
graceful
forced
immediate
scheduled
periodic
continuous
batch
streaming
real-time
asynchronous
synchronous
parallel
concurrent
sequential
serial
distributed
centralized
decentralized
federated
clustered
replicated
sharded
partitioned
indexed
cached
compressed
encrypted
signed
verified
validated
sanitized
normalized
formatted
parsed
serialized
deserialized
encoded
decoded
hashed
salted
peppered
obfuscated
minified
optimized
compressed
decompressed
archived
extracted
imported
exported
migrated
synchronized
replicated
backed-up
restored
recovered
repaired
maintained
updated
upgraded
patched
fixed
enhanced
improved
optimized
refactored
restructured
reorganized
redesigned
reimplemented
rewritten
replaced
deprecated
removed
deleted
purged
cleaned
cleared
reset
initialized
configured
customized
personalized
localized
internationalized
translated
adapted
integrated
connected
linked
associated
related
referenced
mapped
indexed
catalogued
classified
categorized
tagged
labeled
annotated
documented
described
explained
illustrated
demonstrated
exemplified
tested
verified
validated
certified
approved
authorized
permitted
allowed
enabled
activated
started
launched
deployed
published
released
distributed
delivered
installed
configured
setup
initialized
bootstrapped
seeded
populated
loaded
imported
migrated
synchronized
updated
refreshed
reloaded
restarted
stopped
paused
resumed
suspended
terminated
killed
destroyed
removed
uninstalled
undeployed
unpublished
withdrawn
recalled
revoked
disabled
deactivated
blocked
banned
blacklisted
whitelisted
filtered
monitored
tracked
logged
audited
reported
analyzed
evaluated
assessed
reviewed
inspected
examined
investigated
diagnosed
debugged
profiled
benchmarked
tested
validated
verified
certified
approved
rejected
accepted
denied
granted
revoked
suspended
restored
recovered
repaired
fixed
patched
updated
upgraded
enhanced
improved
optimized
tuned
calibrated
adjusted
configured
customized
personalized
adapted
modified
changed
altered
transformed
converted
translated
migrated
ported
integrated
merged
combined
unified
consolidated
aggregated
summarized
compressed
archived
backed-up
exported
imported
synchronized
replicated
distributed
shared
published
broadcast
announced
notified
alerted
warned
informed
communicated
transmitted
sent
received
processed
handled
managed
controlled
governed
regulated
supervised
monitored
observed
watched
tracked
followed
guided
directed
instructed
commanded
ordered
requested
asked
queried
searched
found
discovered
identified
recognized
detected
located
positioned
placed
stored
saved
preserved
maintained
kept
held
retained
cached
buffered
queued
scheduled
planned
organized
arranged
structured
formatted
styled
designed
created
generated
produced
manufactured
built
constructed
assembled
compiled
linked
packaged
bundled
deployed
installed
configured
setup
initialized
started
launched
executed
run
operated
performed
conducted
carried-out
implemented
realized
achieved
accomplished
completed
finished
ended
terminated
stopped
paused
suspended
resumed
continued
proceeded
advanced
progressed
developed
evolved
improved
enhanced
optimized
refined
polished
perfected
finalized
concluded
closed
sealed
locked
secured
protected
defended
guarded
shielded
covered
hidden
concealed
masked
disguised
camouflaged
encrypted
encoded
obfuscated
compressed
minimized
reduced
simplified
streamlined
automated
mechanized
digitized
computerized
electronified
virtualized
cloudified
containerized
microserviced
serverless
edge-computed
distributed
decentralized
federated
peer-to-peer
blockchain
cryptocurrency
smart-contract
artificial-intelligence
machine-learning
deep-learning
neural-network
natural-language-processing
computer-vision
robotics
automation
internet-of-things
augmented-reality
virtual-reality
mixed-reality
extended-reality
quantum-computing
edge-computing
fog-computing
cloud-computing
hybrid-cloud
multi-cloud
serverless-computing
function-as-a-service
platform-as-a-service
infrastructure-as-a-service
software-as-a-service
database-as-a-service
monitoring-as-a-service
security-as-a-service
backup-as-a-service
disaster-recovery-as-a-service
business-continuity-as-a-service
compliance-as-a-service
governance-as-a-service
risk-management-as-a-service
identity-as-a-service
access-management-as-a-service
single-sign-on
multi-factor-authentication
biometric-authentication
passwordless-authentication
zero-trust-security
defense-in-depth
security-by-design
privacy-by-design
data-protection
data-privacy
data-governance
data-quality
data-lineage
data-catalog
data-discovery
data-classification
data-masking
data-anonymization
data-pseudonymization
data-encryption
data-compression
data-deduplication
data-archiving
data-retention
data-deletion
data-purging
data-migration
data-synchronization
data-replication
data-backup
data-recovery
data-restoration
disaster-recovery
business-continuity
high-availability
fault-tolerance
resilience
scalability
elasticity
performance
efficiency
optimization
tuning
monitoring
observability
logging
tracing
debugging
profiling
benchmarking
testing
quality-assurance
continuous-integration
continuous-deployment
continuous-delivery
devops
gitops
infrastructure-as-code
configuration-as-code
policy-as-code
security-as-code
compliance-as-code
documentation-as-code
everything-as-code
