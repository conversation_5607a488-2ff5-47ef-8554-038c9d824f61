@echo off
chcp 65001 >nul

REM Pulsex 测试靶场启动脚本 (Windows)

echo 🎯 Pulsex 测试靶场启动脚本
echo ================================

REM 检查Go是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 错误: 未找到Go语言环境，请先安装Go
    pause
    exit /b 1
)

echo ✅ Go语言环境检查通过

REM 检查依赖
echo 📦 检查并安装依赖...
go mod tidy

if %errorlevel% neq 0 (
    echo ❌ 错误: 依赖安装失败
    pause
    exit /b 1
)

echo ✅ 依赖安装完成

REM 设置端口
set PORT=%1
if "%PORT%"=="" set PORT=8888

echo 🚀 启动测试靶场...
echo 📍 访问地址: http://localhost:%PORT%
echo 🛑 按 Ctrl+C 停止服务
echo ================================

REM 启动服务
go run main.go %PORT%

pause