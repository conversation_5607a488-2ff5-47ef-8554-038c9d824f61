# Pulsex 配置文件
# 自动化XSS扫描框架配置
#
# 自定义请求头配置说明:
# 1. 全局配置: 在 crawler.headers 中配置，所有引擎(Colly/Pulse/CrawlerGo)都会使用
# 2. 引擎特定配置: 在各引擎的 headers 中配置，只对该引擎生效
# 3. 优先级: 引擎特定配置 > 全局配置
# 4. 常用场景:
#    - JWT认证: Authorization: "Bearer your_token"
#    - API Key: X-API-Key: "your_api_key"
#    - 会话保持: Cookie: "session=abc123"
#    - 绕过检测: User-Agent: "Custom-Agent/1.0"

# 基础配置
debug: false
log_level: "info"
output_dir: "./reports"

# 爬虫配置
crawler:
  concurrency: 10
  timeout: "30s"
  user_agent: ""
  max_depth: 3

  # 全局随机User-Agent配置
  random_user_agent:
    enable: true                     # 是否启用随机User-Agent
    override_engine_settings: true   # 是否覆盖各引擎的UA设置

  # 全局自定义请求头配置 (所有引擎生效)
  # headers:                         # 全局自定义HTTP请求头
  #   Authorization: "Bearer your_global_token"    # 全局JWT Token认证
  #   X-API-Key: "your_global_api_key"             # 全局API Key认证
  #   X-Custom-Header: "global_value"              # 全局自定义请求头
  cookies: "JSESSIONID=05848D8F641897A0D684F49CD0B7527D; JWT_TOKEN=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpYXQiOjE3NTM4NjM4MzAsImV4cCI6MTc1Mzk1MDIzMCwidXNlcm5hbWUiOiJhZG1pbiJ9.0cQyuaMmRUTDfMQHEQsZ_3fIDU7xt8-2mTKBlcnqH0o"                      # 全局自定义Cookie (格式: "key1=value1; key2=value2")

  # 实际使用示例 (取消注释并修改值):
  # headers:
  #   Authorization: "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  #   X-API-Key: "sk-1234567890abcdef"
  #   X-Forwarded-For: "127.0.0.1"
  #   User-Agent: "Mozilla/5.0 (Custom Scanner)"
  # cookies: "JSESSIONID=ABC123; auth_token=xyz789"
  
  # Colly爬虫配置
  colly:
    enable: true
    max_depth: 3                     # 最大爬取深度
    async: true                      # 是否启用异步模式
    max_pages: 1000                  # 最大页面数

    # 速率限制配置
    rate_limit:
      enable: true                   # 是否启用速率限制
      delay: "1s"                    # 请求间隔
      parallelism: 2                 # 并发数
      randomize_delay: true          # 随机化延迟

    # 代理配置
    proxy:
      enable: false                  # 是否启用代理
      url: ""                        # 代理URL (如: http://127.0.0.1:8080, socks5://127.0.0.1:1080)
      username: ""                   # 代理用户名 (可选)
      password: ""                   # 代理密码 (可选)

    # 请求配置
    request:
      timeout: "30s"                 # 请求超时时间
      user_agent: ""                 # 用户代理 (空字符串表示使用随机用户代理)
      random_user_agent: true        # 是否使用随机用户代理
      follow_redirects: true         # 是否跟随重定向
      max_redirects: 10              # 最大重定向次数

    # 请求头配置 (可以添加自定义请求头)
    headers:
      Accept: "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8"
      Accept-Language: "en-US,en;q=0.5"
      Accept-Encoding: "gzip, deflate"
      DNT: "1"
      Connection: "keep-alive"
      Upgrade-Insecure-Requests: "1"
      # 自定义请求头示例 (取消注释并修改值)
      # Authorization: "Bearer your_token_here"     # JWT Token认证
      # X-API-Key: "your_api_key_here"              # API Key认证
      # X-Forwarded-For: "127.0.0.1"               # 伪造来源IP
      # Custom-Header: "custom_value"               # 自定义请求头
      # Cookie: "session=abc123; token=xyz789"      # 自定义Cookie

    # Cookie配置
    cookies:
      enable: true                   # 是否启用Cookie处理
      jar: true                      # 是否使用Cookie Jar

    # 过滤配置
    filters:
      allowed_domains: []            # 允许的域名列表 (空表示不限制)
      disallowed_domains: []         # 禁止的域名列表
      url_filters: []                # URL过滤正则表达式

    # 缓存配置
    cache:
      enable: false                  # 是否启用缓存
      dir: "./cache/colly"           # 缓存目录

    # 调试配置
    debug:
      enable: false                  # 是否启用调试模式
      log_requests: true             # 是否记录请求日志
      log_responses: false           # 是否记录响应日志

  # Pulse深度爬虫配置
  pulse:
    enable: true                     # 是否启用Pulse
    max_depth: 5                     # 最大爬取深度
    concurrency: 10                  # 并发数
    timeout: 30                      # 超时时间(秒)
    rate_limit: 150                  # 速率限制(请求/分钟)

    # 自定义请求头配置
    headers:                         # 自定义HTTP请求头
      # Authorization: "Bearer your_token_here"     # JWT Token认证
      # X-API-Key: "your_api_key_here"              # API Key认证
      # X-Forwarded-For: "127.0.0.1"               # 伪造来源IP
      # User-Agent: "Custom-Agent/1.0"              # 自定义User-Agent
      # Referer: "https://trusted-site.com"         # 自定义Referer
    cookies: ""                      # 自定义Cookie (格式: "key1=value1; key2=value2")

    # JavaScript渲染配置
    javascript:
      enable: true                   # 是否启用JS渲染
      timeout: 10                    # JS执行超时时间

    # 表单处理配置
    forms:
      auto_fill: true                # 是否自动填充表单
      submit: false                  # 是否自动提交表单

    # 目录爆破配置
    directory_bruteforce:
      enable: true                   # 是否启用目录爆破
      wordlist: "common"             # 字典类型 (common/medium/large)

    # 过滤配置
    filters:
      extensions: ["php", "asp", "aspx", "jsp", "do", "action"]
      status_codes: [200, 301, 302, 403, 500]

  # CrawlerGo JavaScript渲染爬虫配置
  crawlergo:
    enable: true                     # 是否启用CrawlerGo
    max_depth: 3                     # 最大爬取深度
    concurrency: 5                   # 并发数
    timeout: 30                      # 超时时间(秒)
    chrome_path: ""                  # Chrome浏览器路径 (空表示自动检测)
    chrome_args:                     # Chrome启动参数
      - "--no-sandbox"
      - "--disable-gpu"
      - "--disable-dev-shm-usage"
      - "--disable-extensions"
      - "--disable-plugins"
      - "--headless"
    ignore_https_err: true           # 是否忽略HTTPS错误
    max_tab_count: 10                # 最大标签页数量
    tab_run_timeout: 20              # 单个标签页运行超时时间

    # 注意: CrawlerGo会自动使用全局配置(crawler.headers)中的自定义请求头
    # 如需CrawlerGo特定的请求头配置，可以在这里添加

    # 事件触发配置
    event_trigger:
      enable: true                   # 是否启用事件触发
      click_button: true             # 是否点击按钮
      click_link: true               # 是否点击链接
      input_text: true               # 是否输入文本
      submit_form: true              # 是否提交表单

    # 表单处理配置
    form_submit:
      enable: true                   # 是否启用表单提交
      auto_fill: true                # 是否自动填充表单
      submit: false                  # 是否实际提交表单

    # 过滤配置
    filters:
      skip_extensions: ["jpg", "jpeg", "png", "gif", "css", "js", "ico", "svg", "woff", "woff2", "ttf", "eot"]
      allowed_domains: []            # 允许的域名 (空表示不限制)
      blocked_domains: []            # 阻止的域名

    # 输出配置
    output:
      json: true                     # 是否输出JSON格式
      verbose: false                 # 是否显示详细输出
      silent: false                  # 是否静默模式

  # GAU配置
  gau:
    enable: false
    max_results_per_source: 1000
    timeout: 10                    # 超时时间(秒)
    threads: 10                     # 并发线程数
    retries: 2                     # 重试次数
    include_subdomains: false      # 是否包含子域名
    remove_parameters: true        # 是否移除URL参数
    verbose: false                 # 是否显示详细输出

    # 代理配置
    proxy:
      enable: false                # 是否启用代理
      url: ""                      # 代理URL (如: http://127.0.0.1:8080, socks5://127.0.0.1:1080)
      username: ""                 # 代理用户名 (可选)
      password: ""                 # 代理密码 (可选)

    # 数据源配置
    sources:
      - "wayback"                  # Wayback Machine
      - "commoncrawl"              # Common Crawl
      - "otx"                      # AlienVault OTX
      - "urlscan"                  # URLScan.io
      # - "grep"                     # 搜索引擎聚合                  # Wayback Machine

    # URLScan.io API配置
    urlscan:
      api_key: ""                  # URLScan.io API密钥

    # 过滤配置
    filters:
      # 状态码过滤
      match_status_codes: []       # 匹配的状态码 (如: ["200", "301"])
      filter_status_codes: []      # 过滤的状态码 (如: ["404", "403"])

      # MIME类型过滤
      match_mime_types: []         # 匹配的MIME类型
      filter_mime_types:           # 过滤的MIME类型
        - "image/png"
        - "image/jpg"
        - "image/jpeg"
        - "image/gif"
        - "image/svg+xml"
        - "text/css"
        - "application/javascript"

      # 时间范围过滤
      from: ""                     # 开始时间 (格式: YYYY-MM-DD)
      to: ""                       # 结束时间 (格式: YYYY-MM-DD)

    # 黑名单扩展名
    blacklist_extensions:
      - "jpg"
      - "jpeg"
      - "png"
      - "gif"
      - "bmp"
      - "svg"
      - "ico"
      - "css"
      - "js"
      - "woff"
      - "woff2"
      - "ttf"
      - "eot"
      - "pdf"
      - "doc"
      - "docx"
      - "zip"
      - "rar"
      - "tar"
      - "gz"
      - "mp3"
      - "mp4"
      - "avi"

# XSS检测配置
xss:
  enable: true
  min_confidence_score: 0.7
  max_payloads: 50
  param_fuzzing: true
  ast_analysis: true
  context_aware: true

# 目录扫描配置
dirscan:
  enable: true
  extensions:
    - "php"
    - "asp"
    - "aspx"
    - "jsp"
    - "js"
    - "html"
  wordlists:
    - "common_dirs.txt"
    - "xss_dirs.txt"
  concurrency: 20

# JavaScript分析配置
jsanalyzer:
  enable: true
  sensitive_info: true
  api_endpoints: true
  unauthorized_test: true

# 代理配置
proxy:
  enable: false
  port: 8080
  host: "127.0.0.1"

# 报告配置
reporter:
  enable: true
  formats:
    - "markdown"
    - "html"
  template: "default"
