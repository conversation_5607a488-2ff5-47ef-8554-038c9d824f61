package runner

import (
	"context"
	"fmt"
	"sync"

	"github.com/lc/gau/v2/pkg/providers"
	"github.com/lc/gau/v2/pkg/providers/commoncrawl"
	"github.com/lc/gau/v2/pkg/providers/grep"
	"github.com/lc/gau/v2/pkg/providers/otx"
	"github.com/lc/gau/v2/pkg/providers/urlscan"
	"github.com/lc/gau/v2/pkg/providers/wayback"
	"github.com/sirupsen/logrus"
)

type Runner struct {
	sync.WaitGroup

	Providers []providers.Provider
	threads   uint
	ctx       context.Context
}

// Init initializes the runner
func (r *Runner) Init(c *providers.Config, providers []string, filters providers.Filters) error {
	r.threads = c.Threads
	successCount := 0

	for _, name := range providers {
		switch name {
		case "urlscan":
			r.Providers = append(r.Providers, urlscan.New(c))
			logrus.Debugf("成功初始化 %s provider", name)
			successCount++
		case "otx":
			r.Providers = append(r.Providers, otx.New(c))
			logrus.Debugf("成功初始化 %s provider", name)
			successCount++
		case "wayback":
			r.Providers = append(r.Providers, wayback.New(c, filters))
			logrus.Debugf("成功初始化 %s provider", name)
			successCount++
		case "commoncrawl":
			cc, err := commoncrawl.New(c, filters)
			if err != nil {
				logrus.Warnf("跳过 %s provider: %v", name, err)
				continue // 跳过失败的provider，继续处理其他的
			}
			r.Providers = append(r.Providers, cc)
			logrus.Debugf("成功初始化 %s provider", name)
			successCount++
		case "grep":
			r.Providers = append(r.Providers, grep.New(c))
			logrus.Debugf("成功初始化 %s provider", name)
			successCount++

		default:
			logrus.Warnf("未知的provider: %s", name)
		}
	}

	// 检查是否至少有一个provider成功初始化
	if len(r.Providers) == 0 {
		return fmt.Errorf("所有providers初始化失败")
	}

	logrus.Infof("初始化完成，可用数据源: %d/%d", successCount, len(providers))
	return nil
}

// Starts starts the worker
func (r *Runner) Start(ctx context.Context, workChan chan Work, results chan URLResult) {
	for i := uint(0); i < r.threads; i++ {
		r.Add(1)
		go func() {
			defer r.Done()
			r.worker(ctx, workChan, results)
		}()
	}
}

// URLResult 包含URL和发现它的provider信息
type URLResult struct {
	URL      string
	Provider string
}

type Work struct {
	domain   string
	provider providers.Provider
}

func NewWork(domain string, provider providers.Provider) Work {
	return Work{domain, provider}
}

func (w *Work) Do(ctx context.Context, results chan URLResult) error {
	// 创建一个临时通道来接收原始的URL字符串
	urlChan := make(chan string, 100)
	done := make(chan error, 1)

	// 启动goroutine来执行Fetch
	go func() {
		done <- w.provider.Fetch(ctx, w.domain, urlChan)
		close(urlChan)
	}()

	// 转换URL字符串为URLResult
	for {
		select {
		case url, ok := <-urlChan:
			if !ok {
				// urlChan已关闭，等待Fetch完成
				return <-done
			}
			select {
			case results <- URLResult{
				URL:      url,
				Provider: w.provider.Name(),
			}:
			case <-ctx.Done():
				return ctx.Err()
			}
		case <-ctx.Done():
			return ctx.Err()
		}
	}
}

// worker checks to see if the context is finished and executes the fetching process for each provider
func (r *Runner) worker(ctx context.Context, workChan chan Work, results chan URLResult) {
	for {
		select {
		case <-ctx.Done():
			return
		case work, ok := <-workChan:
			if !ok {
				return
			}
			if err := work.Do(ctx, results); err != nil {
				logrus.WithField("provider", work.provider.Name()).Warnf("%s - %v", work.domain, err)
			}
		}
	}
}
