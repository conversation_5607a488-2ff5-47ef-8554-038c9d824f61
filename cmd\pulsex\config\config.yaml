# Pulsex 配置文件

# 全局配置
global:
  version: "1.0.0"
  debug: false
  log_level: "info"
  log_file: "logs/pulsex.log"
  max_threads: 50
  timeout: 30
  user_agent: "Pulsex/1.0.0 (Security Scanner)"
  proxy: ""

# 爬虫配置
crawler:
  # GAU爬虫配置
  gau:
    enabled: true
    max_pages: 10000
    timeout: 60
    providers: ["wayback", "commoncrawl", "otx", "urlscan"]
    blacklist_extensions: ["jpg", "jpeg", "png", "gif", "css", "js", "ico", "svg", "woff", "woff2", "ttf", "eot"]
    
  # Colly爬虫配置
  colly:
    enabled: true
    max_depth: 3
    max_pages: 1000
    delay: 100ms
    random_delay: 50ms
    parallelism: 10
    user_agent: "Pulsex/1.0.0 (Security Scanner)"
    ignore_robots_txt: true
    enable_debug: false
    
  # Katana爬虫配置
  katana:
    enabled: false
    max_depth: 5
    max_pages: 1000
    timeout: 300s
    js_crawl: true
    form_fill: true
    headless: true
    extension_filter: ["jpg", "jpeg", "png", "gif", "css", "ico", "svg", "woff", "woff2", "ttf", "eot"]

# 资产合并配置
merger:
  enable_deduplication: true
  normalize_urls: true
  remove_parameters: false
  keep_parameters: ["id", "page", "category", "type"]
  ignore_extensions: ["jpg", "jpeg", "png", "gif", "css", "ico", "svg", "woff", "woff2", "ttf", "eot"]
  priority_order: ["Katana", "Colly", "GAU"]
  max_urls_per_source: 10000
  enable_smart_filtering: true

# 扫描器配置
scanner:
  # XSS扫描器配置
  xss:
    enabled: true
    min_confidence_score: 0.7
    enable_parameter_fuzz: true
    max_payloads: 50
    timeout: 30
    enable_ast: true
    enable_context_aware: true
    custom_payloads: []
    
  # SQL注入扫描器配置
  sqli:
    enabled: true
    max_retries: 6
    diff_tolerance: 0.05
    upper_ratio_bound: 0.98
    lower_ratio_bound: 0.02
    constant_ratio: 0.9
    timeout: 30
    enable_time_blind: true
    time_delay: 5
    
  # 敏感信息扫描器配置
  sensitive:
    enabled: true
    rules_file: "config/rules/sensitive_rules.yml"
    max_file_size: 10485760  # 10MB
    timeout: 30
    case_sensitive: false
    multiline: true
    include_context: true
    context_lines: 3
    custom_keywords: []
    enabled_rules: []
    disabled_rules: []
    
  # 未授权访问扫描器配置
  unauth:
    enabled: true
    test_methods: ["GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"]
    test_paths: ["/admin", "/api", "/dashboard", "/management", "/config"]
    test_headers: ["X-Forwarded-For", "X-Real-IP", "X-Originating-IP", "X-Remote-IP"]
    timeout: 30
    follow_redirects: false
    max_redirects: 5
    test_bypass: true
    test_privilege_escalation: true
    custom_payloads: []
    sensitive_patterns: ["admin", "administrator", "root", "superuser", "config", "database", "password", "secret", "token"]
    
  # 目录扫描器配置
  directory:
    enabled: false
    extensions: ["php", "asp", "aspx", "jsp", "py", "js", "html"]
    wordlists: ["config/dicts/common_dirs.txt"]
    threads: 20
    timeout: 10
    follow_redirects: false
    max_redirects: 5
    status_codes: [200, 301, 302, 403, 500]
    ignore_length: []
    ignore_words: []
    random_agent: false
    delay: 0ms
    force_extensions: false
    recursive: false
    max_depth: 3
    enable_tech_detect: true
    
  # JavaScript分析器配置
  js:
    enabled: true
    extract_endpoints: true
    extract_secrets: true
    extract_urls: true
    extract_functions: true
    extract_variables: true
    extract_comments: true
    extract_libraries: true
    deobfuscate: false
    beautify: false
    max_file_size: 10485760  # 10MB
    timeout: 30
    custom_patterns: []

# 代理服务器配置
proxy:
  host: "127.0.0.1"
  port: 8080
  cert_file: ""
  key_file: ""
  log_requests: true
  log_file: "logs/proxy.log"
  enable_scan: true
  scan_xss: true
  scan_sqli: true
  scan_sensitive: true
  scan_unauth: true
  include_hosts: []
  exclude_hosts: []
  include_paths: []
  exclude_paths: []
  exclude_extensions: ["jpg", "jpeg", "png", "gif", "css", "js", "ico", "svg", "woff", "woff2", "ttf", "eot"]
  intercept: false
  modify_response: false
  max_body_size: 1048576  # 1MB
  timeout: 30s

# 报告生成配置
report:
  format: "markdown"  # json, markdown, html
  output_dir: "reports"
  filename: "scan_report"
  template: ""
  include_low: true
  include_info: false
  realtime: false

# 日志配置
logging:
  level: "info"  # debug, info, warn, error
  file: "logs/pulsex.log"
  max_size: 100  # MB
  max_backups: 3
  max_age: 28  # days
  compress: true
  console: true

# 性能配置
performance:
  max_concurrent_scans: 10
  max_concurrent_crawls: 5
  request_rate_limit: 100  # requests per second
  memory_limit: 1024  # MB
  cpu_limit: 80  # percentage

# 过滤配置
filters:
  # URL过滤
  url:
    max_length: 2048
    blacklist_extensions: ["jpg", "jpeg", "png", "gif", "bmp", "ico", "svg", "css", "js", "woff", "woff2", "ttf", "eot", "pdf", "doc", "docx", "xls", "xlsx", "zip", "rar", "tar", "gz"]
    blacklist_patterns: ["logout", "signout", "delete", "remove", "drop"]
    whitelist_patterns: []
    
  # 响应过滤
  response:
    max_size: 10485760  # 10MB
    min_size: 0
    blacklist_content_types: ["image/", "video/", "audio/", "application/octet-stream"]
    whitelist_content_types: ["text/", "application/json", "application/xml"]

# 输出配置
output:
  formats: ["markdown", "json", "html"]
  include_screenshots: false
  include_raw_requests: false
  include_raw_responses: false
  compress_reports: false
  
# 通知配置
notifications:
  enabled: false
  webhook_url: ""
  email:
    enabled: false
    smtp_server: ""
    smtp_port: 587
    username: ""
    password: ""
    from: ""
    to: []
  slack:
    enabled: false
    webhook_url: ""
    channel: ""
  discord:
    enabled: false
    webhook_url: ""

# 数据库配置（可选）
database:
  enabled: false
  type: "sqlite"  # sqlite, mysql, postgresql
  host: "localhost"
  port: 3306
  username: ""
  password: ""
  database: "pulsex"
  ssl_mode: "disable"

# 插件配置
plugins:
  enabled: false
  directory: "plugins"
  auto_load: true
  whitelist: []
  blacklist: []

# 更新配置
update:
  auto_check: true
  check_interval: 24h
  update_server: "https://api.github.com/repos/pulsex/pulsex/releases/latest"
  
# 安全配置
security:
  api_key: ""
  rate_limiting: true
  max_requests_per_minute: 1000
  enable_cors: false
  allowed_origins: []
  
# 缓存配置
cache:
  enabled: true
  type: "memory"  # memory, redis, file
  ttl: 3600  # seconds
  max_size: 1000  # entries
  redis:
    host: "localhost"
    port: 6379
    password: ""
    database: 0
