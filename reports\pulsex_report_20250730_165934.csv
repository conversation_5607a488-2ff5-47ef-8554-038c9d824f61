﻿Pulsex扫描报告
目标,http://************:8888/
扫描时间,2025-07-30 16:59:34
耗时,754.2283ms

统计摘要
项目,数量
发现URL,48
JavaScript文件,4
参数,0
表单,0
<PERSON><PERSON>,0
漏洞,0

发现的URL
URL,来源,状态码,标题,风险等级
"http://************:8888/","Pulse",200,"","INFO"
"http://************:8888/index","Pulse",200,"","INFO"
"http://************:8888/index/sqli/jdbc","Pulse",200,"","HIGH"
"http://************:8888/index/sqli/mybatis","Pulse",200,"","HIGH"
"http://************:8888/index/upload","Pulse",200,"","MEDIUM"
"http://************:8888/index/traversal","Pulse",200,"","INFO"
"http://************:8888/index/jwt","Pulse",200,"","INFO"
"http://************:8888/index/captcha","Pulse",200,"","INFO"
"http://************:8888/index/xss","Pulse",200,"","INFO"
"http://************:8888/index/xss/store","Pulse",200,"","INFO"
"http://************:8888/index/ssrf","Pulse",200,"","INFO"
"http://************:8888/index/rce","Pulse",200,"","INFO"
"http://************:8888/index/deserialize","Pulse",200,"","INFO"
"http://************:8888/index/spel","Pulse",200,"","INFO"
"http://************:8888/index/xxe","Pulse",200,"","INFO"
"http://************:8888/index/idor","Pulse",200,"","INFO"
"http://************:8888/index/unauth","Pulse",200,"","INFO"
"http://************:8888/index/ssti","Pulse",200,"","INFO"
"http://************:8888/index/jndi","Pulse",200,"","INFO"
"http://************:8888/index/xstream","Pulse",200,"","INFO"
"http://************:8888/index/fastjson","Pulse",200,"","INFO"
"http://************:8888/index/jackson","Pulse",200,"","INFO"
"http://************:8888/index/log4j","Pulse",200,"","INFO"
"http://************:8888/index/redirect","Pulse",200,"","INFO"
"http://************:8888/index/actuator","Pulse",200,"","INFO"
"http://************:8888/index/xff","Pulse",200,"","INFO"
"http://************:8888/index/swagger","Pulse",200,"","INFO"
"http://************:8888/index/cors","Pulse",200,"","INFO"
"http://************:8888/index/dos","Pulse",200,"","INFO"
"http://************:8888/index/xpath","Pulse",200,"","INFO"
"http://************:8888/index/csv","Pulse",200,"","INFO"
"http://************:8888/index/csrf","Pulse",200,"","INFO"
"http://************:8888/SQLI/MyBatis/vul/order?field=id&sort=desc,abs(111111)","Pulse",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/order?field=id","Pulse",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/id/1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/query?user=admin","Pulse",200,"","HIGH"
"http://************:8888/js/jquery-3.6.0.min.js","Pulse",200,"","INFO"
"http://************:8888/js/bootstrap-4.6.0.min.js","Pulse",200,"","INFO"
"http://************:8888/js/codemirror.js","Pulse",200,"","INFO"
"http://************:8888/js/groovy.js","Pulse",200,"","INFO"
"http://************:8888/SQLI/MyBatis/vul/search","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul1?id=1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul2?id=2%20or%201=1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul3?id=2","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe2?id=1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe1?id=1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe3?id=1","Pulse",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe4?id=1","Pulse",200,"","HIGH"
"http://************:8888/","Merged",200,"","INFO"
"http://************:8888/index","Merged",200,"","INFO"
"http://************:8888/index/sqli/jdbc","Merged",200,"","HIGH"
"http://************:8888/index/sqli/mybatis","Merged",200,"","HIGH"
"http://************:8888/index/upload","Merged",200,"","MEDIUM"
"http://************:8888/index/traversal","Merged",200,"","INFO"
"http://************:8888/index/jwt","Merged",200,"","INFO"
"http://************:8888/index/captcha","Merged",200,"","INFO"
"http://************:8888/index/xss","Merged",200,"","INFO"
"http://************:8888/index/xss/store","Merged",200,"","INFO"
"http://************:8888/index/ssrf","Merged",200,"","INFO"
"http://************:8888/index/rce","Merged",200,"","INFO"
"http://************:8888/index/deserialize","Merged",200,"","INFO"
"http://************:8888/index/spel","Merged",200,"","INFO"
"http://************:8888/index/xxe","Merged",200,"","INFO"
"http://************:8888/index/idor","Merged",200,"","INFO"
"http://************:8888/index/unauth","Merged",200,"","INFO"
"http://************:8888/index/ssti","Merged",200,"","INFO"
"http://************:8888/index/jndi","Merged",200,"","INFO"
"http://************:8888/index/xstream","Merged",200,"","INFO"
"http://************:8888/index/fastjson","Merged",200,"","INFO"
"http://************:8888/index/jackson","Merged",200,"","INFO"
"http://************:8888/index/log4j","Merged",200,"","INFO"
"http://************:8888/index/redirect","Merged",200,"","INFO"
"http://************:8888/index/actuator","Merged",200,"","INFO"
"http://************:8888/index/xff","Merged",200,"","INFO"
"http://************:8888/index/swagger","Merged",200,"","INFO"
"http://************:8888/index/cors","Merged",200,"","INFO"
"http://************:8888/index/dos","Merged",200,"","INFO"
"http://************:8888/index/xpath","Merged",200,"","INFO"
"http://************:8888/index/csv","Merged",200,"","INFO"
"http://************:8888/index/csrf","Merged",200,"","INFO"
"http://************:8888/SQLI/MyBatis/vul/order?field=id&sort=desc,abs(111111)","Merged",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/order?field=id","Merged",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/id/1","Merged",200,"","HIGH"
"http://************:8888/SQLI/MyBatis/safe/query?user=admin","Merged",200,"","HIGH"
"http://************:8888/js/jquery-3.6.0.min.js","Merged",200,"","INFO"
"http://************:8888/js/bootstrap-4.6.0.min.js","Merged",200,"","INFO"
"http://************:8888/js/codemirror.js","Merged",200,"","INFO"
"http://************:8888/js/groovy.js","Merged",200,"","INFO"
"http://************:8888/SQLI/MyBatis/vul/search","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul1?id=1","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul2?id=2%20or%201=1","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/vul3?id=2","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe2?id=1","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe1?id=1","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe3?id=1","Merged",200,"","HIGH"
"http://************:8888/SQLI/JDBC/safe4?id=1","Merged",200,"","HIGH"

JavaScript文件分析
文件URL,大小,发现端点数,敏感信息数
"http://************:8888/js/jquery-3.6.0.min.js",0,0,0
"http://************:8888/js/bootstrap-4.6.0.min.js",0,0,0
"http://************:8888/js/codemirror.js",0,0,0
"http://************:8888/js/groovy.js",0,0,0
"http://************:8888/js/jquery-3.6.0.min.js",0,0,0
"http://************:8888/js/bootstrap-4.6.0.min.js",0,0,0
"http://************:8888/js/codemirror.js",0,0,0
"http://************:8888/js/groovy.js",0,0,0
