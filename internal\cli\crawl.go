package cli

import (
	"context"
	"fmt"
	"os"
	"strings"

	"github.com/spf13/cobra"
	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/crawler"
	"Pulsex/pkg/report"
)

var (
	crawlURL      string
	crawlFile     string
	crawlThreads  int
	crawlTimeout  int
	crawlEngine   string
	crawlDepth    int
	crawlOutput   string
	generateReport bool
	reportFormat   string
	customHeaders  []string
	customCookies  string
)

// crawlCmd 爬虫+XSS综合扫描命令
var crawlCmd = &cobra.Command{
	Use:   "crawl",
	Short: "🕷️ 执行爬虫收集任务/XSS扫描引擎",
	Long: `🕷️ 执行爬虫收集任务/XSS扫描引擎

这是Pulsex的核心功能，集成了多种爬虫引擎和XSS检测技术：

🔧 爬虫引擎:
  • Colly: 高性能网页抓取 + 智能目录遍历
  • Pulse: 深度JavaScript分析和表单处理
  • GAU: 历史URL数据挖掘
  • CrawlerGo: JavaScript渲染爬虫 + 动态内容发现

🔍 XSS检测:
  • AST语法验证
  • 智能无害探测
  • 上下文感知分析
  • 参数模糊测试

📝 使用示例:
  pulsex crawl -u https://example.com              # 单目标扫描
  pulsex crawl -f targets.txt                     # 批量目标扫描
  pulsex crawl -u https://example.com -e all      # 使用所有引擎
  pulsex crawl -u https://example.com -t 20       # 设置线程数
  pulsex crawl -u https://example.com -d 5        # 设置爬取深度
  pulsex crawl -u https://example.com -o results  # 指定输出目录
  pulsex crawl -u https://example.com -H "Authorization: Bearer token123"  # 全局自定义请求头
  pulsex crawl -u https://example.com -C "session=abc123"                  # 全局自定义Cookie
  pulsex crawl -u https://example.com -e all -H "X-API-Key: key123"        # 所有引擎使用自定义头`,
	RunE: func(cmd *cobra.Command, args []string) error {
		cfg := getConfig()
		if cfg == nil {
			return fmt.Errorf("配置未初始化")
		}

		// 打印模块状态
		printModuleStatus()

		// 验证参数
		if crawlURL == "" && crawlFile == "" {
			return fmt.Errorf("必须指定 -u (URL) 或 -f (文件)")
		}

		if crawlURL != "" && crawlFile != "" {
			return fmt.Errorf("不能同时指定 -u 和 -f 参数")
		}

		// 获取目标列表
		var targets []string
		if crawlURL != "" {
			targets = []string{crawlURL}
		} else {
			var err error
			targets, err = readTargetsFromFile(crawlFile)
			if err != nil {
				return fmt.Errorf("读取目标文件失败: %v", err)
			}
		}

		if len(targets) == 0 {
			return fmt.Errorf("没有找到有效的目标")
		}

		logger.Infof("开始扫描 %d 个目标", len(targets))

		// 处理自定义请求头
		if err := applyCustomHeaders(cfg); err != nil {
			return fmt.Errorf("设置自定义请求头失败: %v", err)
		}

		// 创建上下文
		ctx := context.Background()

		// 执行扫描
		for i, target := range targets {
			logger.PrintProgress(i+1, len(targets), fmt.Sprintf("扫描目标: %s", target))

			if err := scanTarget(ctx, target, cfg); err != nil {
				logger.Errorf("扫描目标 %s 失败: %v", target, err)
				continue
			}
		}

		logger.Success("扫描完成")
		return nil
	},
}

func init() {
	// 目标设置
	crawlCmd.Flags().StringVarP(&crawlURL, "url", "u", "", "🎯 目标URL")
	crawlCmd.Flags().StringVarP(&crawlFile, "file", "f", "", "📁 目标文件路径 (每行一个URL)")

	// 引擎设置
	crawlCmd.Flags().StringVarP(&crawlEngine, "engine", "e", "all", "🔧 爬虫引擎 (gau/colly/pulse/crawlergo/all)")

	// 性能设置
	crawlCmd.Flags().IntVarP(&crawlThreads, "threads", "t", 10, "🚀 并发线程数")
	crawlCmd.Flags().IntVar(&crawlTimeout, "timeout", 30, "⏱️ 超时时间(秒)")
	crawlCmd.Flags().IntVar(&crawlDepth, "depth", 3, "🔍 爬取深度")

	// 输出设置
	crawlCmd.Flags().StringVar(&crawlOutput, "output-file", "", "📄 输出文件路径")

	// 报告设置
	crawlCmd.Flags().BoolVarP(&generateReport, "report", "r", true, "📊 生成扫描报告")
	crawlCmd.Flags().StringVar(&reportFormat, "report-format", "all", "📋 报告格式 (html/json/csv/all)")

	// 自定义请求头设置 (全局生效，所有引擎都会使用)
	crawlCmd.Flags().StringArrayVarP(&customHeaders, "header", "H", []string{}, "🔧 全局自定义请求头 (格式: 'Key: Value', 所有引擎生效)")
	crawlCmd.Flags().StringVarP(&customCookies, "cookie", "C", "", "🍪 全局自定义Cookie (所有引擎生效)")

	// 标记必需参数
	crawlCmd.MarkFlagsMutuallyExclusive("url", "file")
}

// applyCustomHeaders 应用自定义请求头到全局配置
func applyCustomHeaders(cfg *config.Config) error {
	// 处理自定义Headers - 应用到全局配置
	if len(customHeaders) > 0 {
		if cfg.Crawler.Headers == nil {
			cfg.Crawler.Headers = make(map[string]string)
		}

		for _, header := range customHeaders {
			parts := strings.SplitN(header, ":", 2)
			if len(parts) != 2 {
				return fmt.Errorf("无效的请求头格式: %s (应为 'Key: Value')", header)
			}

			key := strings.TrimSpace(parts[0])
			value := strings.TrimSpace(parts[1])
			cfg.Crawler.Headers[key] = value
			logger.Infof("设置全局自定义请求头: %s = %s", key, value)
		}
	}

	// 处理自定义Cookies - 应用到全局配置
	if customCookies != "" {
		cfg.Crawler.Cookies = customCookies
		logger.Infof("设置全局自定义Cookie: %s", customCookies)
	}

	return nil
}

// readTargetsFromFile 从文件读取目标列表
func readTargetsFromFile(filename string) ([]string, error) {
	content, err := os.ReadFile(filename)
	if err != nil {
		return nil, err
	}

	lines := strings.Split(string(content), "\n")
	var targets []string
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line != "" && !strings.HasPrefix(line, "#") {
			targets = append(targets, line)
		}
	}

	return targets, nil
}

// scanTarget 扫描单个目标
func scanTarget(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🎯 开始扫描目标: %s", target)

	// 根据引擎参数选择爬虫引擎
	switch crawlEngine {
	case "gau":
		return runGAUEngine(ctx, target, cfg)
	case "colly":
		return runCollyEngine(ctx, target, cfg)
	case "pulse":
		return runPulseEngine(ctx, target, cfg)
	case "crawlergo":
		return runCrawlerGoEngine(ctx, target, cfg)
	case "all":
		return runAllEngines(ctx, target, cfg)
	default:
		return fmt.Errorf("不支持的爬虫引擎: %s", crawlEngine)
	}
}

// runGAUEngine 运行GAU引擎
func runGAUEngine(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🔍 启动GAU引擎")
	gauEngine := crawler.NewGAUEngine(cfg)

	result, err := gauEngine.Crawl(ctx, target)
	if err != nil {
		return fmt.Errorf("GAU爬取失败: %v", err)
	}

	logger.Infof("✅ GAU完成，发现 %d 个URL", len(result.URLs))

	// 生成报告
	if generateReport {
		multiResult := &crawler.MultiEngineResult{
			GAUResult:     result,
			MergedResult:  result,
			TotalDuration: result.Duration,
		}
		if err := generateScanReport(target, multiResult); err != nil {
			logger.Warnf("生成报告失败: %v", err)
		}
	}

	return nil
}

// runCollyEngine 运行Colly引擎
func runCollyEngine(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🕷️ 启动Colly引擎")
	collyEngine := crawler.NewCollyEngine(cfg)

	result, err := collyEngine.Crawl(ctx, target)
	if err != nil {
		return fmt.Errorf("Colly爬取失败: %v", err)
	}

	logger.Infof("✅ Colly完成，发现 %d 个URL", len(result.URLs))

	// 生成报告
	if generateReport {
		multiResult := &crawler.MultiEngineResult{
			CollyResult:   result,
			MergedResult:  result,
			TotalDuration: result.Duration,
		}
		if err := generateScanReport(target, multiResult); err != nil {
			logger.Warnf("生成报告失败: %v", err)
		}
	}

	return nil
}

// runPulseEngine 运行Pulse引擎
func runPulseEngine(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🕸️ 启动Pulse引擎")
	pulseEngine := crawler.NewPulseEngine(cfg)

	result, err := pulseEngine.Crawl(ctx, target)
	if err != nil {
		return fmt.Errorf("Pulse爬取失败: %v", err)
	}

	logger.Infof("✅ Pulse完成，发现 %d 个URL", len(result.URLs))

	// 生成报告
	if generateReport {
		// 创建一个模拟的MultiEngineResult
		multiResult := &crawler.MultiEngineResult{
			PulseResult:   result,
			MergedResult:  result,
			TotalDuration: result.Duration,
		}
		if err := generateScanReport(target, multiResult); err != nil {
			logger.Warnf("生成报告失败: %v", err)
		}
	}

	return nil
}

// runCrawlerGoEngine 运行CrawlerGo引擎
func runCrawlerGoEngine(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🤖 启动CrawlerGo引擎")
	crawlergoEngine := crawler.NewCrawlerGoEngine(cfg)

	result, err := crawlergoEngine.Crawl(ctx, target)
	if err != nil {
		return fmt.Errorf("CrawlerGo爬取失败: %v", err)
	}

	logger.Infof("✅ CrawlerGo完成，发现 %d 个URL", len(result.URLs))
	return nil
}

// runAllEngines 运行所有引擎
func runAllEngines(ctx context.Context, target string, cfg *config.Config) error {
	logger.Infof("🚀 启动多引擎协同爬虫")
	multiEngine := crawler.NewMultiEngineCrawler(cfg)

	result, err := multiEngine.CrawlWithAllEngines(ctx, target)
	if err != nil {
		return fmt.Errorf("多引擎爬取失败: %v", err)
	}

	// 显示各引擎结果
	if result.GAUResult != nil {
		logger.Infof("📚 GAU: 发现 %d 个URL", len(result.GAUResult.URLs))
	}
	if result.CollyResult != nil {
		logger.Infof("🕷️ Colly: 发现 %d 个URL", len(result.CollyResult.URLs))
	}
	if result.PulseResult != nil {
		logger.Infof("🕸️ Pulse: 发现 %d 个URL", len(result.PulseResult.URLs))
	}
	if result.CrawlerGoResult != nil {
		logger.Infof("🤖 CrawlerGo: 发现 %d 个URL", len(result.CrawlerGoResult.URLs))
	}
	if result.MergedResult != nil {
		logger.Infof("🎯 合并结果: 总共发现 %d 个URL", len(result.MergedResult.URLs))
	}

	logger.Infof("⏱️ 总耗时: %v", result.TotalDuration)

	// 生成报告
	if generateReport {
		if err := generateScanReport(target, result); err != nil {
			logger.Warnf("生成报告失败: %v", err)
		}
	}

	return nil
}

// generateScanReport 生成扫描报告
func generateScanReport(target string, result *crawler.MultiEngineResult) error {
	logger.Infof("📊 开始生成扫描报告...")

	// 创建报告生成器
	outputDir := "reports"
	if crawlOutput != "" {
		outputDir = crawlOutput
	}

	reportGen := report.NewReportGenerator(outputDir)

	// 转换数据
	reportData := report.ConvertMultiEngineResult(target, result)

	// 根据格式生成报告
	switch reportFormat {
	case "html":
		return reportGen.GenerateHTMLReport(reportData)
	case "json":
		return reportGen.GenerateJSONReport(reportData)
	case "csv":
		return reportGen.GenerateCSVReport(reportData)
	case "all":
		return reportGen.GenerateReport(reportData)
	default:
		return fmt.Errorf("不支持的报告格式: %s", reportFormat)
	}
}
