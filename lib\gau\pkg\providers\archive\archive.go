package archive

import (
	"context"
	"fmt"
	"net"
	"regexp"
	"strings"

	"github.com/lc/gau/v2/pkg/httpclient"
	"github.com/lc/gau/v2/pkg/providers"
	"github.com/sirupsen/logrus"
)

const (
	Name = "archive"
)

type Client struct {
	config *providers.Config
}

func New(c *providers.Config) *Client {
	return &Client{config: c}
}

func (c *Client) Name() string {
	return Name
}

func (c *Client) Fetch(ctx context.Context, domain string, results chan string) error {
	// 检查是否为IP地址，Archive.today主要用于域名查询
	if net.ParseIP(domain) != nil {
		logrus.WithFields(logrus.Fields{"provider": Name}).Infof("跳过IP地址 %s，Archive.today主要支持域名查询", domain)
		return nil
	}

	// Archive.today搜索URL
	searchURL := fmt.Sprintf("https://archive.today/search/?q=%s", domain)
	
	logrus.WithFields(logrus.Fields{"provider": Name}).Infof("fetching %s", domain)
	
	resp, err := httpclient.MakeRequest(c.config.Client, searchURL, c.config.MaxRetries, c.config.Timeout)
	if err != nil {
		return fmt.Errorf("failed to fetch archive.today: %s", err)
	}

	// 解析HTML响应，提取URL
	content := string(resp)
	urls := c.extractURLs(content, domain)
	
	for _, url := range urls {
		select {
		case <-ctx.Done():
			return nil
		case results <- url:
		}
	}

	logrus.WithFields(logrus.Fields{"provider": Name}).Infof("found %d URLs for %s", len(urls), domain)
	return nil
}

func (c *Client) extractURLs(content, domain string) []string {
	var urls []string
	
	// 匹配Archive.today页面中的URL链接
	// 这是一个简化的正则表达式，实际使用时可能需要更复杂的解析
	re := regexp.MustCompile(`https?://[^"'\s<>]+` + regexp.QuoteMeta(domain) + `[^"'\s<>]*`)
	matches := re.FindAllString(content, -1)
	
	seen := make(map[string]bool)
	for _, match := range matches {
		// 清理URL
		url := strings.TrimSpace(match)
		if url != "" && !seen[url] && strings.Contains(url, domain) {
			urls = append(urls, url)
			seen[url] = true
		}
	}
	
	return urls
}
