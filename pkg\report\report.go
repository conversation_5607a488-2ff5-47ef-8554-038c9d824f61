package report

import (
	"embed"
	"encoding/json"
	"fmt"
	"html/template"
	"os"
	"path/filepath"
	"time"
)

//go:embed templates/*.html
var templateFS embed.FS

// ReportGenerator 报告生成器
type ReportGenerator struct {
	OutputDir string
	Timestamp string
}

// NewReportGenerator 创建报告生成器
func NewReportGenerator(outputDir string) *ReportGenerator {
	if outputDir == "" {
		outputDir = "reports"
	}
	
	// 确保输出目录存在
	os.MkdirAll(outputDir, 0755)
	
	return &ReportGenerator{
		OutputDir: outputDir,
		Timestamp: time.Now().Format("20060102_150405"),
	}
}

// ReportData 报告数据结构
type ReportData struct {
	// 基本信息
	Target      string    `json:"target"`
	ScanTime    time.Time `json:"scan_time"`
	Duration    string    `json:"duration"`
	Version     string    `json:"version"`
	
	// 统计信息
	Summary     SummaryData     `json:"summary"`
	
	// 爬虫结果
	CrawlResults CrawlResultsData `json:"crawl_results"`
	
	// XSS检测结果
	XSSResults  []XSSResult     `json:"xss_results"`
	
	// 漏洞统计
	Vulnerabilities VulnerabilityStats `json:"vulnerabilities"`
	
	// 风险评估
	RiskAssessment RiskAssessment `json:"risk_assessment"`
}

// SummaryData 摘要数据
type SummaryData struct {
	TotalURLs       int `json:"total_urls"`
	TotalJSFiles    int `json:"total_js_files"`
	TotalParameters int `json:"total_parameters"`
	TotalForms      int `json:"total_forms"`
	TotalCookies    int `json:"total_cookies"`
	TotalSubdomains int `json:"total_subdomains"`
	
	// 引擎统计
	GAUURLs       int `json:"gau_urls"`
	CollyURLs     int `json:"colly_urls"`
	PulseURLs     int `json:"pulse_urls"`
	CrawlerGoURLs int `json:"crawlergo_urls"`
}

// CrawlResultsData 爬虫结果数据
type CrawlResultsData struct {
	URLs         []URLInfo      `json:"urls"`
	JSFiles      []JSFileInfo   `json:"js_files"`
	Parameters   []ParameterInfo `json:"parameters"`
	Forms        []FormInfo     `json:"forms"`
	Cookies      []CookieInfo   `json:"cookies"`
	Subdomains   []string       `json:"subdomains"`
	Technologies []string       `json:"technologies"`
}

// URLInfo URL信息
type URLInfo struct {
	URL        string `json:"url"`
	Source     string `json:"source"`
	StatusCode int    `json:"status_code"`
	Title      string `json:"title"`
	Length     int    `json:"length"`
	RiskLevel  string `json:"risk_level"`
}

// JSFileInfo JS文件信息
type JSFileInfo struct {
	URL       string   `json:"url"`
	Size      int      `json:"size"`
	Endpoints []string `json:"endpoints"`
	Secrets   []string `json:"secrets"`
}

// ParameterInfo 参数信息
type ParameterInfo struct {
	Name      string `json:"name"`
	Value     string `json:"value"`
	Source    string `json:"source"`
	Type      string `json:"type"`
	RiskLevel string `json:"risk_level"`
}

// FormInfo 表单信息
type FormInfo struct {
	Action   string            `json:"action"`
	Method   string            `json:"method"`
	Fields   map[string]string `json:"fields"`
	Source   string            `json:"source"`
}

// CookieInfo Cookie信息
type CookieInfo struct {
	Name     string `json:"name"`
	Value    string `json:"value"`
	Domain   string `json:"domain"`
	Path     string `json:"path"`
	Secure   bool   `json:"secure"`
	HttpOnly bool   `json:"httponly"`
}

// XSSResult XSS检测结果
type XSSResult struct {
	URL         string    `json:"url"`
	Parameter   string    `json:"parameter"`
	Payload     string    `json:"payload"`
	Type        string    `json:"type"`
	Severity    string    `json:"severity"`
	Confirmed   bool      `json:"confirmed"`
	Evidence    string    `json:"evidence"`
	Timestamp   time.Time `json:"timestamp"`
	Method      string    `json:"method"`
	StatusCode  int       `json:"status_code"`
	Response    string    `json:"response"`
}

// VulnerabilityStats 漏洞统计
type VulnerabilityStats struct {
	Critical int `json:"critical"`
	High     int `json:"high"`
	Medium   int `json:"medium"`
	Low      int `json:"low"`
	Info     int `json:"info"`
	Total    int `json:"total"`
}

// RiskAssessment 风险评估
type RiskAssessment struct {
	OverallRisk   string             `json:"overall_risk"`
	RiskScore     int                `json:"risk_score"`
	RiskFactors   []string           `json:"risk_factors"`
	Recommendations []string         `json:"recommendations"`
	Categories    map[string]int     `json:"categories"`
}

// GenerateReport 生成完整报告
func (rg *ReportGenerator) GenerateReport(data *ReportData) error {
	// 生成HTML报告
	if err := rg.GenerateHTMLReport(data); err != nil {
		return fmt.Errorf("生成HTML报告失败: %v", err)
	}
	
	// 生成JSON报告
	if err := rg.GenerateJSONReport(data); err != nil {
		return fmt.Errorf("生成JSON报告失败: %v", err)
	}
	
	// 生成CSV报告
	if err := rg.GenerateCSVReport(data); err != nil {
		return fmt.Errorf("生成CSV报告失败: %v", err)
	}
	
	return nil
}

// GenerateHTMLReport 生成HTML报告
func (rg *ReportGenerator) GenerateHTMLReport(data *ReportData) error {
	// 读取模板
	tmplContent, err := templateFS.ReadFile("templates/report.html")
	if err != nil {
		return fmt.Errorf("读取HTML模板失败: %v", err)
	}

	// 解析模板
	tmpl, err := template.New("report").Parse(string(tmplContent))
	if err != nil {
		return fmt.Errorf("解析HTML模板失败: %v", err)
	}

	// 创建输出文件
	filename := filepath.Join(rg.OutputDir, fmt.Sprintf("pulsex_report_%s.html", rg.Timestamp))
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建HTML报告文件失败: %v", err)
	}
	defer file.Close()

	// 执行模板
	if err := tmpl.Execute(file, data); err != nil {
		return fmt.Errorf("执行HTML模板失败: %v", err)
	}

	fmt.Printf("📄 HTML报告已生成: %s\n", filename)
	return nil
}

// GenerateJSONReport 生成JSON报告
func (rg *ReportGenerator) GenerateJSONReport(data *ReportData) error {
	filename := filepath.Join(rg.OutputDir, fmt.Sprintf("pulsex_report_%s.json", rg.Timestamp))
	
	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建JSON报告文件失败: %v", err)
	}
	defer file.Close()
	
	encoder := json.NewEncoder(file)
	encoder.SetIndent("", "  ")
	
	if err := encoder.Encode(data); err != nil {
		return fmt.Errorf("编码JSON报告失败: %v", err)
	}
	
	fmt.Printf("📄 JSON报告已生成: %s\n", filename)
	return nil
}

// GenerateCSVReport 生成CSV报告
func (rg *ReportGenerator) GenerateCSVReport(data *ReportData) error {
	filename := filepath.Join(rg.OutputDir, fmt.Sprintf("pulsex_report_%s.csv", rg.Timestamp))

	file, err := os.Create(filename)
	if err != nil {
		return fmt.Errorf("创建CSV报告文件失败: %v", err)
	}
	defer file.Close()

	// 写入BOM以支持Excel中文显示
	file.WriteString("\xEF\xBB\xBF")

	// 写入基本信息
	file.WriteString("Pulsex扫描报告\n")
	file.WriteString(fmt.Sprintf("目标,%s\n", data.Target))
	file.WriteString(fmt.Sprintf("扫描时间,%s\n", data.ScanTime.Format("2006-01-02 15:04:05")))
	file.WriteString(fmt.Sprintf("耗时,%s\n", data.Duration))
	file.WriteString("\n")

	// 写入统计摘要
	file.WriteString("统计摘要\n")
	file.WriteString("项目,数量\n")
	file.WriteString(fmt.Sprintf("发现URL,%d\n", data.Summary.TotalURLs))
	file.WriteString(fmt.Sprintf("JavaScript文件,%d\n", data.Summary.TotalJSFiles))
	file.WriteString(fmt.Sprintf("参数,%d\n", data.Summary.TotalParameters))
	file.WriteString(fmt.Sprintf("表单,%d\n", data.Summary.TotalForms))
	file.WriteString(fmt.Sprintf("Cookie,%d\n", data.Summary.TotalCookies))
	file.WriteString(fmt.Sprintf("漏洞,%d\n", data.Vulnerabilities.Total))
	file.WriteString("\n")

	// 写入URL详情
	file.WriteString("发现的URL\n")
	file.WriteString("URL,来源,状态码,标题,风险等级\n")
	for _, url := range data.CrawlResults.URLs {
		file.WriteString(fmt.Sprintf("\"%s\",\"%s\",%d,\"%s\",\"%s\"\n",
			url.URL, url.Source, url.StatusCode, url.Title, url.RiskLevel))
	}
	file.WriteString("\n")

	// 写入XSS漏洞详情
	if len(data.XSSResults) > 0 {
		file.WriteString("XSS漏洞详情\n")
		file.WriteString("URL,参数,载荷,类型,严重程度,确认状态,发现时间\n")
		for _, xss := range data.XSSResults {
			file.WriteString(fmt.Sprintf("\"%s\",\"%s\",\"%s\",\"%s\",\"%s\",%t,\"%s\"\n",
				xss.URL, xss.Parameter, xss.Payload, xss.Type, xss.Severity,
				xss.Confirmed, xss.Timestamp.Format("2006-01-02 15:04:05")))
		}
		file.WriteString("\n")
	}

	// 写入JavaScript文件分析
	if len(data.CrawlResults.JSFiles) > 0 {
		file.WriteString("JavaScript文件分析\n")
		file.WriteString("文件URL,大小,发现端点数,敏感信息数\n")
		for _, js := range data.CrawlResults.JSFiles {
			file.WriteString(fmt.Sprintf("\"%s\",%d,%d,%d\n",
				js.URL, js.Size, len(js.Endpoints), len(js.Secrets)))
		}
	}

	fmt.Printf("📄 CSV报告已生成: %s\n", filename)
	return nil
}


