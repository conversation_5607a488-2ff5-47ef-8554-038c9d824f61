name: Release gau

on:
  push:
    tags:
      - "*"

jobs:
  build:
    name: GoReleaser build
    runs-on: ubuntu-latest

    steps:
      - name: Check out code into the Go module directory
        uses: actions/checkout@v2
        with:
          fetch-depth: 0 # See: https://goreleaser.com/ci/actions/

      - name: Set up Go 1.23.2
        uses: actions/setup-go@v2
        with:
          go-version: 1.23.2
        id: go

      - name: Import GPG key
        id: import_gpg
        uses: crazy-max/ghaction-import-gpg@v4
        with:
          gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.GPG_PASSPHRASE }}

      - name: <PERSON> GoReleaser
        uses: goreleaser/goreleaser-action@master
        with:
          version: latest
          args: release --clean
        env:
          GITHUB_TOKEN: ${{ secrets.GO_RELEASER_GITHUB_TOKEN }}
          GPG_FINGERPRINT: ${{ steps.import_gpg.outputs.fingerprint }}
