<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pulsex 扫描报告 - http://192.168.11.1:8888/</title>
    <style>
         
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
            line-height: 1.5;
            color: #111827;
            background: #f9fafb;
            display: flex;
            min-height: 100vh;
            font-weight: 400;
        }

         
        .sidebar {
            width: 260px;
            background: #ffffff;
            color: #374151;
            padding: 20px;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            overflow-x: hidden;
            z-index: 1000;
            box-shadow: 0 0 20px rgba(0,0,0,0.05);
            border-right: 1px solid #e5e7eb;
            transition: width 0.3s ease;
        }

        .sidebar.collapsed {
            width: 70px;
            padding: 20px 10px;
        }

        .sidebar.collapsed .sidebar-header h1,
        .sidebar.collapsed .sidebar-header p,
        .sidebar.collapsed .nav-link span:not(.nav-icon) {
            opacity: 0;
            pointer-events: none;
        }

        .sidebar.collapsed .nav-link {
            justify-content: center;
            padding: 12px;
        }

        .sidebar.collapsed .nav-link i {
            margin-right: 0;
        }

        .sidebar-header {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #e5e7eb;
            position: relative;
            text-align: center;
            overflow: hidden;
        }

        .sidebar-toggle {
            position: absolute;
            top: 15px;
            right: -12px;
            width: 24px;
            height: 24px;
            background: #6366f1;
            border: none;
            border-radius: 4px;
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            transition: background 0.2s ease;
            z-index: 1001;
        }

        .sidebar-toggle:hover {
            background: #4f46e5;
        }

        .sidebar-toggle .toggle-icon {
            transition: transform 0.2s ease;
        }

        .sidebar.collapsed .sidebar-toggle .toggle-icon {
            transform: rotate(180deg);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin-bottom: 4px;
            color: #111827;
            font-weight: 600;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-header h1 {
            opacity: 0;
            transform: scale(0.8);
        }

        .sidebar-header p {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 400;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
        }

        .sidebar.collapsed .sidebar-header p {
            opacity: 0;
            transform: scale(0.8);
        }

         
        .sidebar-icon {
            display: none;
            font-size: 1.5rem;
            color: #6366f1;
            transition: all 0.3s ease;
        }

        .sidebar.collapsed .sidebar-icon {
            display: block;
            opacity: 1;
        }

        .nav-menu {
            list-style: none;
        }

        .nav-item {
            margin-bottom: 6px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            color: #6b7280;
            text-decoration: none;
            padding: 10px 12px;
            border-radius: 6px;
            transition: all 0.2s ease;
            font-size: 0.875rem;
            font-weight: 500;
            white-space: nowrap;
            margin-bottom: 2px;
            overflow: hidden;
        }

        .nav-link:hover {
            background: #f3f4f6;
            color: #374151;
        }

        .nav-link.active {
            background: #6366f1;
            color: white;
        }

        .nav-link i {
            margin-right: 10px;
            width: 16px;
            font-size: 1rem;
            flex-shrink: 0;
            transition: all 0.3s ease;
            text-align: center;
        }

        .sidebar.collapsed .nav-link i {
            margin-right: 0;
        }

        .nav-link span:not(.nav-icon) {
            transition: all 0.3s ease;
            overflow: hidden;
        }

        .sidebar.collapsed .nav-link span:not(.nav-icon) {
            opacity: 0;
            width: 0;
            transform: translateX(-10px);
        }

         
        .nav-link .tooltip {
            position: absolute;
            left: 80px;
            top: 50%;
            transform: translateY(-50%);
            background: #374151;
            color: white;
            padding: 6px 10px;
            border-radius: 4px;
            font-size: 0.75rem;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: all 0.2s ease;
            z-index: 1002;
            pointer-events: none;
        }

        .nav-link .tooltip::before {
            content: '';
            position: absolute;
            left: -4px;
            top: 50%;
            transform: translateY(-50%);
            border: 4px solid transparent;
            border-right-color: #374151;
        }

        .sidebar.collapsed .nav-link:hover .tooltip {
            opacity: 1;
            visibility: visible;
        }

         
        .main-content {
            margin-left: 260px;
            flex: 1;
            padding: 24px;
            max-width: calc(100vw - 260px);
            transition: margin-left 0.3s ease, max-width 0.3s ease;
        }

        .sidebar.collapsed + .main-content {
            margin-left: 70px;
            max-width: calc(100vw - 70px);
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

         
        .section {
            background: white;
            border-radius: 8px;
            padding: 24px;
            margin-bottom: 20px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
        }

        .header-content {
            display: grid;
            grid-template-columns: 1fr 2fr;
            gap: 24px;
            align-items: center;
        }

        .logo h1 {
            font-size: 2rem;
            color: #111827;
            margin-bottom: 4px;
            font-weight: 600;
        }

        .logo p {
            color: #6b7280;
            font-size: 0.875rem;
        }

        .section h2 {
            color: #111827;
            margin-bottom: 20px;
            font-size: 1.25rem;
            font-weight: 600;
            padding-bottom: 8px;
            border-bottom: 1px solid #e5e7eb;
        }

        .info-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
        }

        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            background: #f9fafb;
            border-radius: 6px;
            border-left: 3px solid #6366f1;
        }

        .info-item .label {
            font-weight: 500;
            color: #374151;
            font-size: 0.875rem;
        }

        .info-item .value {
            color: #111827;
            font-family: 'SF Mono', 'Monaco', monospace;
            font-weight: 500;
            font-size: 0.875rem;
        }

         
        .section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }

        .section h2 {
            color: #333;
            margin-bottom: 25px;
            font-size: 1.8em;
            padding-bottom: 10px;
            border-bottom: 2px solid #667eea;
        }

         
        .summary-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }

        .summary-card {
            background: white;
            border-radius: 6px;
            padding: 16px 12px;
            text-align: center;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            border-top: 3px solid #6366f1;
        }

        .summary-card .card-icon {
            font-size: 1.5rem;
            margin-bottom: 8px;
        }

        .summary-card h3 {
            font-size: 1.5rem;
            color: #111827;
            margin-bottom: 4px;
            font-weight: 600;
        }

        .summary-card p {
            color: #6b7280;
            font-weight: 400;
            font-size: 0.75rem;
            margin: 0;
        }

         
        .engine-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }

        .engine-card {
            background: white;
            border-radius: 6px;
            padding: 16px;
            text-align: center;
            border: 1px solid #e5e7eb;
            border-top: 3px solid #6366f1;
        }

        .engine-card h3 {
            color: #111827;
            margin-bottom: 8px;
            font-size: 1rem;
            font-weight: 600;
        }

        .engine-card p {
            color: #6b7280;
            margin-bottom: 12px;
            font-size: 0.75rem;
        }

        .engine-stats {
            font-size: 1.25rem;
            font-weight: 600;
            color: #6366f1;
        }

         
        .table-container {
            overflow-x: auto;
            margin-top: 16px;
            border-radius: 6px;
            border: 1px solid #e5e7eb;
        }

        .data-table {
            width: 100%;
            border-collapse: collapse;
            background: white;
        }

        .data-table th,
        .data-table td {
            padding: 12px 16px;
            text-align: left;
            border-bottom: 1px solid #f3f4f6;
        }

        .data-table th {
            background: #f9fafb;
            font-weight: 500;
            color: #374151;
            cursor: pointer;
            user-select: none;
            font-size: 0.875rem;
        }

        .data-table th:hover {
            background: #f3f4f6;
        }

        .data-table tr:hover {
            background: #f9fafb;
        }

        .data-table tr:last-child td {
            border-bottom: none;
        }

         
        .source-badge {
            background: #6366f1;
            color: white;
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .status-code {
            padding: 2px 6px;
            border-radius: 4px;
            font-weight: 500;
            font-size: 0.75rem;
        }

        .status-200 { background: #dcfce7; color: #166534; }
        .status-301, .status-302 { background: #fef3c7; color: #92400e; }
        .status-403 { background: #fecaca; color: #991b1b; }
        .status-404 { background: #f3f4f6; color: #374151; }
        .status-500 { background: #fecaca; color: #991b1b; }

        .risk-badge {
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 0.75rem;
            font-weight: 500;
        }

        .risk-badge.HIGH { background: #ef4444; color: white; }
        .risk-badge.MEDIUM { background: #f59e0b; color: white; }
        .risk-badge.LOW { background: #10b981; color: white; }
        .risk-badge.INFO { background: #3b82f6; color: white; }

         
        .js-files {
            display: flex;
            flex-direction: column;
            gap: 12px;
        }

        .js-file-item {
            background: #f9fafb;
            border-radius: 6px;
            padding: 16px;
            border-left: 3px solid #6366f1;
        }

        .js-file-item h4 {
            color: #111827;
            margin-bottom: 8px;
            word-break: break-all;
            font-size: 0.875rem;
            font-weight: 600;
        }

        .endpoints,
        .secrets {
            margin-top: 12px;
        }

        .endpoints ul,
        .secrets ul {
            list-style: none;
            margin-top: 8px;
        }

        .endpoints li,
        .secrets li {
            padding: 4px 0;
            color: #374151;
            font-size: 0.875rem;
        }

        .secrets li.secret {
            background: #fef3c7;
            padding: 6px 10px;
            border-radius: 4px;
            margin: 4px 0;
            color: #92400e;
            font-family: monospace;
            font-size: 0.75rem;
        }

         
        .report-footer {
            background: white;
            border-radius: 6px;
            padding: 16px;
            text-align: center;
            border: 1px solid #e5e7eb;
            margin-top: 20px;
        }

        .report-footer p {
            color: #6b7280;
            margin-bottom: 8px;
            font-size: 0.875rem;
        }

         
        .search-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1001;
            background: white;
            border-radius: 24px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            overflow: hidden;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
        }

        .search-container.expanded {
            width: 300px;
            padding: 6px 12px;
        }

        .search-toggle {
            width: 40px;
            height: 40px;
            background: #6366f1;
            border: none;
            border-radius: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            transition: background 0.2s ease;
            flex-shrink: 0;
        }

        .search-toggle:hover {
            background: #4f46e5;
        }

        .search-toggle .icon {
            transition: all 0.2s ease;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
        }

        .search-toggle .icon.search {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .search-toggle .icon.close {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }

        .search-container.expanded .search-toggle .icon.search {
            opacity: 0;
            transform: translate(-50%, -50%) scale(0.8);
        }

        .search-container.expanded .search-toggle .icon.close {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
        }

        .search-content {
            display: flex;
            align-items: center;
            opacity: 0;
            transition: opacity 0.3s ease;
            margin-left: 8px;
            flex: 1;
        }

        .search-container.expanded .search-content {
            opacity: 1;
        }

        #reportSearch {
            padding: 8px 12px;
            border: 1px solid #d1d5db;
            border-radius: 4px;
            flex: 1;
            margin-right: 8px;
            font-size: 0.875rem;
            background: white;
            min-width: 0;
        }

        #reportSearch:focus {
            outline: none;
            border-color: #6366f1;
            box-shadow: 0 0 0 1px #6366f1;
        }

        .clear-search {
            padding: 6px 12px;
            background: #f3f4f6;
            color: #374151;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.75rem;
            white-space: nowrap;
        }

        .clear-search:hover {
            background: #e5e7eb;
        }

        .highlight {
            background: #fef3c7 !important;
            color: #92400e !important;
            font-weight: 500;
        }

         
        .search-results-count {
            position: absolute;
            top: -6px;
            right: -6px;
            background: #ef4444;
            color: white;
            border-radius: 10px;
            padding: 2px 6px;
            font-size: 0.625rem;
            font-weight: 600;
            min-width: 16px;
            text-align: center;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .search-results-count.show {
            opacity: 1;
        }

         
        @media (max-width: 1200px) {
            .summary-grid {
                grid-template-columns: repeat(3, 1fr);
            }

            .engine-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
                width: 280px;
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                max-width: 100vw;
                padding: 15px;
            }

            .header-content {
                grid-template-columns: 1fr;
                text-align: center;
            }

            .info-grid {
                grid-template-columns: 1fr;
            }

            .summary-grid {
                grid-template-columns: repeat(2, 1fr);
            }

            .engine-grid {
                grid-template-columns: 1fr;
            }

            .search-container {
                position: relative;
                top: auto;
                right: auto;
                margin-bottom: 20px;
                width: 50px;
                margin-left: auto;
            }

            .search-container.expanded {
                width: 100%;
            }

            .search-content {
                margin-left: 10px;
            }
        }

        @media (max-width: 480px) {
            .summary-grid {
                grid-template-columns: 1fr;
            }

            .search-container.expanded {
                border-radius: 15px;
                padding: 5px 10px;
            }

            .search-content {
                margin-left: 8px;
            }

            .clear-search {
                padding: 6px 10px;
                font-size: 11px;
            }

            .section {
                padding: 20px;
            }

            .data-table th,
            .data-table td {
                padding: 12px 8px;
                font-size: 0.9em;
            }
        }
    </style>
</head>
<body>
    
    <nav class="sidebar" id="sidebar">
        <button class="sidebar-toggle" onclick="toggleSidebar()">
            <span class="toggle-icon">◀</span>
        </button>
        <div class="sidebar-header">
            <div class="sidebar-icon">🚀</div>
            <h1>🚀 Pulsex</h1>
            <p>扫描报告导航</p>
        </div>
        <ul class="nav-menu">
            <li class="nav-item">
                <a href="#overview" class="nav-link active">
                    <i>📊</i>
                    <span>扫描概览</span>
                    <div class="tooltip">扫描概览</div>
                </a>
            </li>
            <li class="nav-item">
                <a href="#summary" class="nav-link">
                    <i>📈</i>
                    <span>执行摘要</span>
                    <div class="tooltip">执行摘要</div>
                </a>
            </li>
            <li class="nav-item">
                <a href="#engines" class="nav-link">
                    <i>🕷️</i>
                    <span>爬虫引擎</span>
                    <div class="tooltip">爬虫引擎</div>
                </a>
            </li>
            <li class="nav-item">
                <a href="#urls" class="nav-link">
                    <i>🔗</i>
                    <span>发现URL</span>
                    <div class="tooltip">发现URL</div>
                </a>
            </li>
            
            
            
            <li class="nav-item">
                <a href="#forms" class="nav-link">
                    <i>📝</i>
                    <span>表单分析</span>
                    <div class="tooltip">表单分析</div>
                </a>
            </li>
            
        </ul>
    </nav>

    
    <main class="main-content">
        <div class="container">
            
            <div class="search-container" id="searchContainer">
                <button class="search-toggle" onclick="toggleSearch()">
                    <span class="icon search">🔍</span>
                    <span class="icon close">✕</span>
                </button>
                <div class="search-content">
                    <input type="text" id="reportSearch" placeholder="搜索URL、参数、内容..." />
                    <button class="clear-search" onclick="clearSearch()">清除</button>
                </div>
                <div class="search-results-count" id="searchResultsCount">0</div>
            </div>

            
            <section id="overview" class="section">
                <h2>📊 扫描概览</h2>
                <div class="header-content">
                    <div class="logo">
                        <h1>🚀 Pulsex</h1>
                        <p>专业XSS漏洞扫描框架</p>
                    </div>
                    <div class="scan-info">
                        <div class="info-grid">
                            <div class="info-item">
                                <span class="label">目标:</span>
                                <span class="value">http://192.168.11.1:8888/</span>
                            </div>
                            <div class="info-item">
                                <span class="label">扫描时间:</span>
                                <span class="value">2025-07-30 17:00:28</span>
                            </div>
                            <div class="info-item">
                                <span class="label">耗时:</span>
                                <span class="value">825.3961ms</span>
                            </div>
                            <div class="info-item">
                                <span class="label">版本:</span>
                                <span class="value">1.0.0</span>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            
            <section id="summary" class="section">
                <h2>📈 执行摘要</h2>
                <div class="summary-grid">
                    <div class="summary-card">
                        <div class="card-icon">🔗</div>
                        <div class="card-content">
                            <h3>0</h3>
                            <p>发现URL</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📜</div>
                        <div class="card-content">
                            <h3>0</h3>
                            <p>JavaScript文件</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🔧</div>
                        <div class="card-content">
                            <h3>0</h3>
                            <p>参数</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">📝</div>
                        <div class="card-content">
                            <h3>2</h3>
                            <p>表单</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🍪</div>
                        <div class="card-content">
                            <h3>2</h3>
                            <p>Cookie</p>
                        </div>
                    </div>
                    <div class="summary-card">
                        <div class="card-icon">🌐</div>
                        <div class="card-content">
                            <h3>0</h3>
                            <p>子域名</p>
                        </div>
                    </div>
                </div>
            </section>

            
            <section id="engines" class="section">
                <h2>🕷️ 爬虫引擎结果</h2>
                <div class="engine-grid">
                    <div class="engine-card">
                        <h3>📚 GAU引擎</h3>
                        <p>历史URL收集</p>
                        <div class="engine-stats">0 URLs</div>
                    </div>
                    <div class="engine-card">
                        <h3>🕷️ Colly引擎</h3>
                        <p>实时爬取验证</p>
                        <div class="engine-stats">0 URLs</div>
                    </div>
                    <div class="engine-card">
                        <h3>🕸️ Pulse引擎</h3>
                        <p>深度分析挖掘</p>
                        <div class="engine-stats">0 URLs</div>
                    </div>
                    <div class="engine-card">
                        <h3>🤖 CrawlerGo引擎</h3>
                        <p>JavaScript渲染</p>
                        <div class="engine-stats">0 URLs</div>
                    </div>
                </div>
            </section>

            
            <section id="urls" class="section">
                <h2>🔗 发现的URL</h2>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th onclick="sortTable(this, 0)">URL <span class="sort-icon">↕️</span></th>
                                <th onclick="sortTable(this, 1)">来源 <span class="sort-icon">↕️</span></th>
                                <th onclick="sortTable(this, 2)">状态码 <span class="sort-icon">↕️</span></th>
                                <th onclick="sortTable(this, 3)">标题 <span class="sort-icon">↕️</span></th>
                                <th onclick="sortTable(this, 4)">风险等级 <span class="sort-icon">↕️</span></th>
                            </tr>
                        </thead>
                        <tbody>
                            
                        </tbody>
                    </table>
                </div>
            </section>

            
            

            
            

            
            
            <section id="forms" class="section">
                <h2>📝 表单分析</h2>
                <div class="js-files">
                    
                    <div class="js-file-item">
                        <h4>表单: /user/login;jsessionid=8AB048C38654D751F8115133504AA306</h4>
                        <p><strong>方法:</strong> POST</p>
                        <p><strong>来源:</strong> <span class="source-badge">Colly</span></p>
                        
                        <div class="endpoints">
                            <strong>表单字段:</strong>
                            <ul>
                                
                                <li><strong>captcha:</strong> text</li>
                                
                                <li><strong>password:</strong> password</li>
                                
                                <li><strong>username:</strong> text</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    
                    <div class="js-file-item">
                        <h4>表单: /user/login;jsessionid=8AB048C38654D751F8115133504AA306</h4>
                        <p><strong>方法:</strong> POST</p>
                        <p><strong>来源:</strong> <span class="source-badge">Merged</span></p>
                        
                        <div class="endpoints">
                            <strong>表单字段:</strong>
                            <ul>
                                
                                <li><strong>captcha:</strong> text</li>
                                
                                <li><strong>password:</strong> password</li>
                                
                                <li><strong>username:</strong> text</li>
                                
                            </ul>
                        </div>
                        
                    </div>
                    
                </div>
            </section>
            

            
            <footer class="report-footer">
                <p>报告由 <strong>Pulsex v1.0.0</strong> 生成 | 生成时间: 2025-07-30 17:00:28</p>
                <p>⚠️ 本报告仅供安全测试使用，请勿用于非法用途</p>
            </footer>
        </div>
    </main>

    <script>
        
        document.addEventListener('DOMContentLoaded', function() {
            initializeReport();
            addInteractivity();
            addSearchFunctionality();
        });

        
        function initializeReport() {
            console.log('🚀 Pulsex 报告已加载');

            
            initSidebarState();

            
            const navLinks = document.querySelectorAll('.nav-link');
            navLinks.forEach(link => {
                link.addEventListener('click', function(e) {
                    e.preventDefault();

                    
                    navLinks.forEach(l => l.classList.remove('active'));
                    
                    this.classList.add('active');

                    
                    const targetId = this.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            
            window.addEventListener('scroll', updateActiveNav);

            
            document.body.style.opacity = '0';
            setTimeout(() => {
                document.body.style.transition = 'opacity 0.5s ease';
                document.body.style.opacity = '1';
            }, 100);
        }

        
        function updateActiveNav() {
            const sections = document.querySelectorAll('section[id]');
            const navLinks = document.querySelectorAll('.nav-link');

            let currentSection = '';
            sections.forEach(section => {
                const rect = section.getBoundingClientRect();
                if (rect.top <= 100 && rect.bottom >= 100) {
                    currentSection = section.id;
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + currentSection) {
                    link.classList.add('active');
                }
            });
        }

        
        function addInteractivity() {
            
            addHighlighting();
        }

        
        function sortTable(header, columnIndex) {
            const table = header.closest('table');
            const tbody = table.querySelector('tbody');
            const rows = Array.from(tbody.querySelectorAll('tr'));

            const isAscending = table.dataset.sortOrder !== 'asc';
            table.dataset.sortOrder = isAscending ? 'asc' : 'desc';

            rows.sort((a, b) => {
                const aValue = a.cells[columnIndex].textContent.trim();
                const bValue = b.cells[columnIndex].textContent.trim();

                
                if (!isNaN(aValue) && !isNaN(bValue)) {
                    return isAscending ? aValue - bValue : bValue - aValue;
                }

                
                return isAscending ?
                    aValue.localeCompare(bValue) :
                    bValue.localeCompare(aValue);
            });

            
            rows.forEach(row => tbody.appendChild(row));

            
            const headers = table.querySelectorAll('th .sort-icon');
            headers.forEach((icon, index) => {
                if (index === columnIndex) {
                    icon.innerHTML = isAscending ? ' ↑' : ' ↓';
                } else {
                    icon.innerHTML = ' ↕️';
                }
            });
        }

        
        function addHighlighting() {
            
            const urlCells = document.querySelectorAll('.data-table td:first-child a');
            urlCells.forEach(link => {
                const url = link.href;
                if (isDangerousURL(url)) {
                    link.style.color = '#dc3545';
                    link.style.fontWeight = 'bold';
                }
            });
        }

        
        function isDangerousURL(url) {
            const dangerousPatterns = [
                /admin/i, /login/i, /password/i, /config/i, /backup/i, /debug/i,
                /\.env/i, /api.*admin/i, /\.\.\/etc/i
            ];
            return dangerousPatterns.some(pattern => pattern.test(url));
        }

        
        function addSearchFunctionality() {
            const searchInput = document.getElementById('reportSearch');
            searchInput.addEventListener('input', performSearch);

            
            document.addEventListener('click', function(e) {
                const searchContainer = document.getElementById('searchContainer');
                if (!searchContainer.contains(e.target) && searchContainer.classList.contains('expanded')) {
                    const searchInput = document.getElementById('reportSearch');
                    if (searchInput.value === '') {
                        toggleSearch();
                    }
                }
            });
        }

        
        function toggleSearch() {
            const searchContainer = document.getElementById('searchContainer');
            const searchInput = document.getElementById('reportSearch');

            if (searchContainer.classList.contains('expanded')) {
                
                searchContainer.classList.remove('expanded');
                searchInput.blur();
                clearSearch();
            } else {
                
                searchContainer.classList.add('expanded');
                setTimeout(() => {
                    searchInput.focus();
                }, 400);
            }
        }

        
        function performSearch() {
            const searchTerm = document.getElementById('reportSearch').value.toLowerCase();
            const searchableElements = document.querySelectorAll('.data-table td, .js-file-item, .info-item');
            const resultCounter = document.getElementById('searchResultsCount');

            
            document.querySelectorAll('.highlight').forEach(el => {
                el.classList.remove('highlight', 'scrolled');
            });

            if (searchTerm.length < 2) {
                resultCounter.classList.remove('show');
                return;
            }

            let matchCount = 0;
            let firstMatch = null;

            searchableElements.forEach(element => {
                const text = element.textContent.toLowerCase();
                if (text.includes(searchTerm)) {
                    element.classList.add('highlight');
                    matchCount++;

                    
                    if (!firstMatch) {
                        firstMatch = element;
                    }
                }
            });

            
            if (matchCount > 0) {
                resultCounter.textContent = matchCount;
                resultCounter.classList.add('show');

                
                if (firstMatch) {
                    firstMatch.classList.add('scrolled');
                    setTimeout(() => {
                        firstMatch.scrollIntoView({
                            behavior: 'smooth',
                            block: 'center',
                            inline: 'nearest'
                        });
                    }, 100);
                }
            } else {
                resultCounter.classList.remove('show');
            }
        }

        
        function clearSearch() {
            const searchInput = document.getElementById('reportSearch');
            const resultCounter = document.getElementById('searchResultsCount');

            searchInput.value = '';
            resultCounter.classList.remove('show');

            document.querySelectorAll('.highlight').forEach(el => {
                el.classList.remove('highlight', 'scrolled');
            });

            
            searchInput.focus();
        }

        
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('collapsed');

            
            const isCollapsed = sidebar.classList.contains('collapsed');
            localStorage.setItem('sidebarCollapsed', isCollapsed);
        }

        
        function toggleMobileSidebar() {
            const sidebar = document.querySelector('.sidebar');
            sidebar.classList.toggle('open');
        }

        
        function initSidebarState() {
            const sidebar = document.getElementById('sidebar');
            const savedState = localStorage.getItem('sidebarCollapsed');

            if (savedState === 'true') {
                sidebar.classList.add('collapsed');
            }
        }

        
        function initMobileAdaptation() {
            if (window.innerWidth <= 768) {
                const sidebar = document.getElementById('sidebar');
                const mainContent = document.querySelector('.main-content');

                
                sidebar.classList.add('collapsed');

                
                const toggleBtn = document.createElement('button');
                toggleBtn.innerHTML = '☰';
                toggleBtn.style.cssText = `
                    position: fixed;
                    top: 20px;
                    left: 20px;
                    z-index: 1002;
                    background: linear-gradient(135deg, #667eea, #764ba2);
                    color: white;
                    border: none;
                    padding: 12px;
                    border-radius: 50%;
                    cursor: pointer;
                    width: 45px;
                    height: 45px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-size: 18px;
                    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
                    transition: all 0.3s ease;
                `;
                toggleBtn.onclick = toggleMobileSidebar;
                document.body.appendChild(toggleBtn);

                
                toggleBtn.addEventListener('mouseenter', () => {
                    toggleBtn.style.transform = 'scale(1.1)';
                    toggleBtn.style.boxShadow = '0 6px 20px rgba(102, 126, 234, 0.4)';
                });

                toggleBtn.addEventListener('mouseleave', () => {
                    toggleBtn.style.transform = 'scale(1)';
                    toggleBtn.style.boxShadow = '0 4px 12px rgba(102, 126, 234, 0.3)';
                });
            }
        }

        
        window.addEventListener('load', initMobileAdaptation);
    </script>
</body>
</html>
