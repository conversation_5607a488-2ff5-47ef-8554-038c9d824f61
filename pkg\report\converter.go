package report

import (
	"fmt"
	"net/url"
	"strings"
	"time"

	"Pulsex/pkg/crawler"
	"Pulsex/pkg/interfaces"
)

// ConvertMultiEngineResult 转换多引擎爬虫结果为报告数据
func ConvertMultiEngineResult(target string, result *crawler.MultiEngineResult) *ReportData {
	reportData := &ReportData{
		Target:    target,
		ScanTime:  time.Now(),
		Duration:  result.TotalDuration.String(),
		Version:   "1.0.0",
		Summary:   SummaryData{},
		CrawlResults: CrawlResultsData{
			URLs:         make([]URLInfo, 0),
			JSFiles:      make([]JSFileInfo, 0),
			Parameters:   make([]ParameterInfo, 0),
			Forms:        make([]FormInfo, 0),
			Cookies:      make([]CookieInfo, 0),
			Subdomains:   make([]string, 0),
			Technologies: make([]string, 0),
		},
		XSSResults:      make([]XSSResult, 0),
		Vulnerabilities: VulnerabilityStats{},
		RiskAssessment:  RiskAssessment{},
	}

	// 转换各引擎结果
	if result.GAUResult != nil {
		convertCrawlResult(result.GAUResult, "GAU", reportData)
		reportData.Summary.GAUURLs = len(result.GAUResult.URLs)
	}

	if result.CollyResult != nil {
		convertCrawlResult(result.CollyResult, "Colly", reportData)
		reportData.Summary.CollyURLs = len(result.CollyResult.URLs)
	}

	if result.PulseResult != nil {
		convertCrawlResult(result.PulseResult, "Pulse", reportData)
		reportData.Summary.PulseURLs = len(result.PulseResult.URLs)
	}

	if result.CrawlerGoResult != nil {
		convertCrawlResult(result.CrawlerGoResult, "CrawlerGo", reportData)
		reportData.Summary.CrawlerGoURLs = len(result.CrawlerGoResult.URLs)
	}

	// 使用合并结果作为主要数据源
	if result.MergedResult != nil {
		convertCrawlResult(result.MergedResult, "Merged", reportData)
	}

	// 计算统计信息
	calculateSummary(reportData)

	// 进行风险评估
	performRiskAssessment(reportData)

	return reportData
}

// convertCrawlResult 转换单个爬虫结果
func convertCrawlResult(crawlResult *interfaces.CrawlResult, source string, reportData *ReportData) {
	// 转换URL
	for _, urlStr := range crawlResult.URLs {
		urlInfo := URLInfo{
			URL:        urlStr,
			Source:     source,
			StatusCode: 200, // 默认状态码
			Title:      "",
			RiskLevel:  assessURLRisk(urlStr),
		}
		reportData.CrawlResults.URLs = append(reportData.CrawlResults.URLs, urlInfo)
	}

	// 转换JS文件
	for _, jsFile := range crawlResult.JSFiles {
		jsInfo := JSFileInfo{
			URL:       jsFile,
			Size:      0, // 需要实际获取
			Endpoints: make([]string, 0),
			Secrets:   make([]string, 0),
		}
		reportData.CrawlResults.JSFiles = append(reportData.CrawlResults.JSFiles, jsInfo)
	}

	// 转换参数
	for name, value := range crawlResult.Parameters {
		paramInfo := ParameterInfo{
			Name:      name,
			Value:     value,
			Source:    source,
			Type:      getParameterType(name, value),
			RiskLevel: assessParameterRisk(name, value),
		}
		reportData.CrawlResults.Parameters = append(reportData.CrawlResults.Parameters, paramInfo)
	}

	// 转换表单
	for _, form := range crawlResult.Forms {
		formInfo := FormInfo{
			Action: form.Action,
			Method: form.Method,
			Fields: form.Fields,
			Source: source,
		}
		reportData.CrawlResults.Forms = append(reportData.CrawlResults.Forms, formInfo)
	}

	// 转换Cookie
	for _, cookie := range crawlResult.Cookies {
		cookieInfo := CookieInfo{
			Name:     cookie.Name,
			Value:    cookie.Value,
			Domain:   cookie.Domain,
			Path:     cookie.Path,
			Secure:   cookie.Secure,
			HttpOnly: cookie.HttpOnly,
		}
		reportData.CrawlResults.Cookies = append(reportData.CrawlResults.Cookies, cookieInfo)
	}

	// 注意：当前CrawlResult结构中没有Subdomains和Technologies字段
	// 这些字段可以在后续版本中添加
}

// assessURLRisk 评估URL风险等级
func assessURLRisk(urlStr string) string {
	u, err := url.Parse(urlStr)
	if err != nil {
		return "INFO"
	}

	path := strings.ToLower(u.Path)
	query := strings.ToLower(u.RawQuery)

	// 高风险模式
	highRiskPatterns := []string{
		"admin", "login", "password", "config", "backup", "debug",
		"../etc/passwd", "../etc/shadow", ".env", "api_keys",
		"database", "sql", "dump", "secret",
	}

	// 中风险模式
	mediumRiskPatterns := []string{
		"api", "upload", "delete", "edit", "modify", "update",
		"user", "account", "profile", "settings",
	}

	// 检查高风险
	for _, pattern := range highRiskPatterns {
		if strings.Contains(path, pattern) || strings.Contains(query, pattern) {
			return "HIGH"
		}
	}

	// 检查中风险
	for _, pattern := range mediumRiskPatterns {
		if strings.Contains(path, pattern) || strings.Contains(query, pattern) {
			return "MEDIUM"
		}
	}

	// 检查是否有参数
	if u.RawQuery != "" {
		return "LOW"
	}

	return "INFO"
}

// assessParameterRisk 评估参数风险等级
func assessParameterRisk(name, value string) string {
	name = strings.ToLower(name)
	value = strings.ToLower(value)

	// 高风险参数
	highRiskParams := []string{
		"password", "passwd", "pwd", "secret", "token", "key",
		"admin", "root", "config", "debug", "sql", "query",
	}

	// 中风险参数
	mediumRiskParams := []string{
		"user", "username", "email", "id", "file", "path",
		"url", "redirect", "callback", "return",
	}

	for _, param := range highRiskParams {
		if strings.Contains(name, param) {
			return "HIGH"
		}
	}

	for _, param := range mediumRiskParams {
		if strings.Contains(name, param) {
			return "MEDIUM"
		}
	}

	return "LOW"
}

// getParameterType 获取参数类型
func getParameterType(name, value string) string {
	name = strings.ToLower(name)

	if strings.Contains(name, "id") || strings.Contains(name, "num") {
		return "数字"
	}
	if strings.Contains(name, "email") || strings.Contains(name, "mail") {
		return "邮箱"
	}
	if strings.Contains(name, "url") || strings.Contains(name, "link") {
		return "URL"
	}
	if strings.Contains(name, "file") || strings.Contains(name, "path") {
		return "文件路径"
	}
	if strings.Contains(name, "token") || strings.Contains(name, "key") {
		return "令牌"
	}

	return "字符串"
}

// calculateSummary 计算统计摘要
func calculateSummary(reportData *ReportData) {
	// 去重URL
	urlMap := make(map[string]bool)
	for _, url := range reportData.CrawlResults.URLs {
		urlMap[url.URL] = true
	}
	reportData.Summary.TotalURLs = len(urlMap)

	// 去重JS文件
	jsMap := make(map[string]bool)
	for _, js := range reportData.CrawlResults.JSFiles {
		jsMap[js.URL] = true
	}
	reportData.Summary.TotalJSFiles = len(jsMap)

	// 去重参数
	paramMap := make(map[string]bool)
	for _, param := range reportData.CrawlResults.Parameters {
		paramMap[param.Name] = true
	}
	reportData.Summary.TotalParameters = len(paramMap)

	reportData.Summary.TotalForms = len(reportData.CrawlResults.Forms)
	reportData.Summary.TotalCookies = len(reportData.CrawlResults.Cookies)

	// 去重子域名
	subdomainMap := make(map[string]bool)
	for _, subdomain := range reportData.CrawlResults.Subdomains {
		subdomainMap[subdomain] = true
	}
	reportData.Summary.TotalSubdomains = len(subdomainMap)
}

// performRiskAssessment 执行风险评估
func performRiskAssessment(reportData *ReportData) {
	riskScore := 0
	riskFactors := make([]string, 0)
	recommendations := make([]string, 0)
	categories := make(map[string]int)

	// 基于发现的内容评估风险
	for _, url := range reportData.CrawlResults.URLs {
		switch url.RiskLevel {
		case "HIGH":
			riskScore += 20
			categories["高风险URL"]++
		case "MEDIUM":
			riskScore += 10
			categories["中风险URL"]++
		case "LOW":
			riskScore += 5
			categories["低风险URL"]++
		}
	}

	// 检查敏感路径
	for _, url := range reportData.CrawlResults.URLs {
		if strings.Contains(strings.ToLower(url.URL), "admin") {
			riskFactors = append(riskFactors, "发现管理员路径")
			recommendations = append(recommendations, "限制管理员路径的访问权限")
		}
		if strings.Contains(strings.ToLower(url.URL), "backup") {
			riskFactors = append(riskFactors, "发现备份文件")
			recommendations = append(recommendations, "移除或保护备份文件")
		}
		if strings.Contains(strings.ToLower(url.URL), "../etc/") {
			riskFactors = append(riskFactors, "发现路径遍历漏洞")
			recommendations = append(recommendations, "修复路径遍历漏洞")
			riskScore += 30
		}
	}

	// 检查敏感参数
	for _, param := range reportData.CrawlResults.Parameters {
		if param.RiskLevel == "HIGH" {
			riskFactors = append(riskFactors, fmt.Sprintf("发现敏感参数: %s", param.Name))
			recommendations = append(recommendations, "对敏感参数进行适当的验证和过滤")
		}
	}

	// 计算总体风险等级
	var overallRisk string
	if riskScore >= 80 {
		overallRisk = "CRITICAL"
	} else if riskScore >= 60 {
		overallRisk = "HIGH"
	} else if riskScore >= 40 {
		overallRisk = "MEDIUM"
	} else if riskScore >= 20 {
		overallRisk = "LOW"
	} else {
		overallRisk = "INFO"
	}

	// 添加通用建议
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "定期进行安全扫描")
		recommendations = append(recommendations, "实施输入验证和输出编码")
		recommendations = append(recommendations, "使用HTTPS加密传输")
	}

	reportData.RiskAssessment = RiskAssessment{
		OverallRisk:     overallRisk,
		RiskScore:       riskScore,
		RiskFactors:     riskFactors,
		Recommendations: recommendations,
		Categories:      categories,
	}

	// 计算漏洞统计（基于XSS结果）
	for _, xss := range reportData.XSSResults {
		switch xss.Severity {
		case "CRITICAL":
			reportData.Vulnerabilities.Critical++
		case "HIGH":
			reportData.Vulnerabilities.High++
		case "MEDIUM":
			reportData.Vulnerabilities.Medium++
		case "LOW":
			reportData.Vulnerabilities.Low++
		default:
			reportData.Vulnerabilities.Info++
		}
	}

	reportData.Vulnerabilities.Total = reportData.Vulnerabilities.Critical +
		reportData.Vulnerabilities.High +
		reportData.Vulnerabilities.Medium +
		reportData.Vulnerabilities.Low +
		reportData.Vulnerabilities.Info
}
