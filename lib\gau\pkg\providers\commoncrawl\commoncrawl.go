package commoncrawl

import (
	"bufio"
	"bytes"
	"context"
	"fmt"
	"io"
	"log"
	"net"
	"time"

	jsoniter "github.com/json-iterator/go"
	"github.com/chromedp/chromedp"
	"github.com/lc/gau/v2/pkg/httpclient"
	"github.com/lc/gau/v2/pkg/providers"
	"github.com/sirupsen/logrus"
)

const (
	Name = "commoncrawl"
)

// verify interface compliance
var _ providers.Provider = (*Client)(nil)

// Client is the structure that holds the Filters and the Client's configuration
type Client struct {
	filters providers.Filters
	config  *providers.Config

	apiURL string
}



// fetchWithBrowser 使用无头浏览器获取CommonCrawl API信息
func fetchWithBrowser(url string) ([]byte, error) {
	logrus.Debugf("使用无头浏览器访问: %s", url)

	// 禁用chromedp的日志输出
	log.SetOutput(io.Discard)

	// 创建Chrome上下文，禁用日志输出
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-logging", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	// 设置超时
	ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	var responseBody string
	err := chromedp.Run(ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible("body", chromedp.ByQuery),
		chromedp.OuterHTML("body", &responseBody),
	)

	if err != nil {
		return nil, fmt.Errorf("无头浏览器访问失败: %v", err)
	}

	// 提取JSON内容（去掉HTML标签）
	// 简单的方法：查找JSON内容
	start := bytes.Index([]byte(responseBody), []byte("["))
	end := bytes.LastIndex([]byte(responseBody), []byte("]"))

	if start == -1 || end == -1 || start >= end {
		return nil, fmt.Errorf("无法从响应中提取JSON内容")
	}

	jsonContent := []byte(responseBody)[start : end+1]
	logrus.Debugf("提取的JSON长度: %d bytes", len(jsonContent))

	return jsonContent, nil
}

func New(c *providers.Config, filters providers.Filters) (*Client, error) {
	logrus.Debugf("正在初始化CommonCrawl，使用无头浏览器访问API")

	// 首先尝试使用传统HTTP请求
	resp, err := httpclient.MakeRequest(c.Client, "http://index.commoncrawl.org/collinfo.json", c.MaxRetries, c.Timeout)
	if err != nil {
		logrus.Debugf("传统HTTP请求失败，尝试使用无头浏览器: %v", err)

		// 使用无头浏览器作为备选方案
		resp, err = fetchWithBrowser("http://index.commoncrawl.org/collinfo.json")
		if err != nil {
			return nil, fmt.Errorf("无头浏览器访问也失败: %v", err)
		}
		logrus.Debugf("无头浏览器访问成功")
	} else {
		logrus.Debugf("传统HTTP请求成功")
	}

	var r apiResult
	if err = jsoniter.Unmarshal(resp, &r); err != nil {
		return nil, fmt.Errorf("error parsing commoncrawl response: %v", err)
	}

	if len(r) == 0 {
		return nil, fmt.Errorf("failed to grab latest commoncrawl index")
	}

	logrus.Debugf("CommonCrawl初始化成功，使用API: %s", r[0].API)
	return &Client{config: c, filters: filters, apiURL: r[0].API}, nil
}

func (c *Client) Name() string {
	return Name
}

// fetchPageWithBrowser 使用无头浏览器获取CommonCrawl页面数据
func (c *Client) fetchPageWithBrowser(url string) ([]byte, error) {
	logrus.Debugf("使用无头浏览器访问CommonCrawl页面: %s", url)

	// 禁用chromedp的日志输出
	log.SetOutput(io.Discard)

	// 创建Chrome上下文，禁用日志输出
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-logging", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-dev-shm-usage", true),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)
	defer cancel()

	ctx, cancel := chromedp.NewContext(allocCtx)
	defer cancel()

	// 设置超时
	ctx, cancel = context.WithTimeout(ctx, 20*time.Second)
	defer cancel()

	var responseBody string
	err := chromedp.Run(ctx,
		chromedp.Navigate(url),
		chromedp.WaitVisible("body", chromedp.ByQuery),
		chromedp.OuterHTML("body", &responseBody),
	)

	if err != nil {
		return nil, fmt.Errorf("无头浏览器访问失败: %v", err)
	}

	// 提取文本内容（去掉HTML标签）
	start := bytes.Index([]byte(responseBody), []byte("{"))
	if start == -1 {
		// 如果没有JSON，可能是纯文本响应
		return []byte(responseBody), nil
	}

	return []byte(responseBody)[start:], nil
}

// Fetch fetches all urls for a given domain and sends them to a channel.
// It returns an error should one occur.
func (c *Client) Fetch(ctx context.Context, domain string, results chan string) error {
	// 检查是否为IP地址，CommonCrawl不支持IP地址查询
	if net.ParseIP(domain) != nil {
		logrus.WithFields(logrus.Fields{"provider": Name}).Infof("跳过IP地址 %s，CommonCrawl仅支持域名查询", domain)
		return nil
	}

	p, err := c.getPagination(domain)
	if err != nil {
		return err
	}
	// 0 pages means no results
	if p.Pages == 0 {
		logrus.WithFields(logrus.Fields{"provider": Name}).Infof("no results for %s", domain)
		return nil
	}

	for page := uint(0); page < p.Pages; page++ {
		select {
		case <-ctx.Done():
			return nil
		default:
			logrus.WithFields(logrus.Fields{"provider": Name, "page": page}).Infof("fetching %s", domain)
			apiURL := c.formatURL(domain, page)

			// 首先尝试传统HTTP请求
			resp, err := httpclient.MakeRequest(c.config.Client, apiURL, 2, 15) // 减少超时时间
			if err != nil {
				logrus.Debugf("传统HTTP请求失败，尝试无头浏览器: %v", err)

				// 使用无头浏览器作为备选方案
				resp, err = c.fetchPageWithBrowser(apiURL)
				if err != nil {
					logrus.Warnf("CommonCrawl页面%d访问失败，跳过: %v", page, err)
					continue // 跳过这一页，继续下一页
				}
			}

			sc := bufio.NewScanner(bytes.NewReader(resp))
			for sc.Scan() {
				var res apiResponse
				if err := jsoniter.Unmarshal(sc.Bytes(), &res); err != nil {
					logrus.Debugf("解析CommonCrawl结果失败: %s", err)
					continue // 跳过这一行，继续下一行
				}
				if res.Error != "" {
					logrus.Warnf("CommonCrawl返回错误: %s", res.Error)
					continue
				}

				results <- res.URL
			}
		}
	}
	return nil
}

func (c *Client) formatURL(domain string, page uint) string {
	if c.config.IncludeSubdomains {
		domain = "*." + domain
	}

	filterParams := c.filters.GetParameters(false)

	return fmt.Sprintf("%s?url=%s/*&output=json&fl=url&page=%d", c.apiURL, domain, page) + filterParams
}

// Fetch the number of pages.
func (c *Client) getPagination(domain string) (r paginationResult, err error) {
	url := fmt.Sprintf("%s&showNumPages=true", c.formatURL(domain, 0))
	var resp []byte

	// 首先尝试传统HTTP请求
	resp, err = httpclient.MakeRequest(c.config.Client, url, 2, 15) // 减少超时时间
	if err != nil {
		logrus.Debugf("获取分页信息失败，尝试无头浏览器: %v", err)

		// 使用无头浏览器作为备选方案
		resp, err = c.fetchPageWithBrowser(url)
		if err != nil {
			logrus.Warnf("无法获取CommonCrawl分页信息: %v", err)
			return r, err
		}
	}

	err = jsoniter.Unmarshal(resp, &r)
	return
}
