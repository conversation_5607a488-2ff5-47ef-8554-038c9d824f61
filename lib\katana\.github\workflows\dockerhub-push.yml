name: 🌥 Docker Push

on:
  workflow_run:
    workflows: ["🎉 Release Binary"]
    types:
      - completed
  workflow_dispatch:

jobs:
  docker:
    runs-on: ubuntu-latest-16-cores
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Get Github tag
        id: meta
        run: |
          curl --silent "https://api.github.com/repos/projectdiscovery/katana/releases/latest" | jq -r .tag_name | xargs -I {} echo TAG={} >> $GITHUB_OUTPUT

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Login to DockerHub
        uses: docker/login-action@v3 
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}

      - name: Build and push
        uses: docker/build-push-action@v6
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: projectdiscovery/katana:latest,projectdiscovery/katana:${{ steps.meta.outputs.TAG }}