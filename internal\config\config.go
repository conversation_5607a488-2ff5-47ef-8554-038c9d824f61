package config

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/spf13/viper"
)

// Config 全局配置结构
type Config struct {
	// 基础配置
	Debug     bool   `mapstructure:"debug"`
	LogLevel  string `mapstructure:"log_level"`
	OutputDir string `mapstructure:"output_dir"`
	
	// 爬虫配置
	Crawler CrawlerConfig `mapstructure:"crawler"`
	
	// XSS检测配置
	XSS XSSConfig `mapstructure:"xss"`
	
	// 目录扫描配置
	DirScan DirScanConfig `mapstructure:"dirscan"`
	
	// JavaScript分析配置
	JSAnalyzer JSAnalyzerConfig `mapstructure:"jsanalyzer"`
	
	// 代理配置
	Proxy ProxyConfig `mapstructure:"proxy"`
	
	// 报告配置
	Reporter ReporterConfig `mapstructure:"reporter"`
}

// CrawlerConfig 爬虫配置
type CrawlerConfig struct {
	Concurrency     int                    `mapstructure:"concurrency"`
	Timeout         time.Duration          `mapstructure:"timeout"`
	UserAgent       string                 `mapstructure:"user_agent"`
	MaxDepth        int                    `mapstructure:"max_depth"`
	RandomUserAgent RandomUserAgentConfig  `mapstructure:"random_user_agent"`

	// 全局自定义请求头配置
	Headers map[string]string `mapstructure:"headers"`
	Cookies string            `mapstructure:"cookies"`

	// Colly配置
	Colly CollyConfig `mapstructure:"colly"`

	// Pulse配置
	Pulse PulseConfig `mapstructure:"pulse"`

	// CrawlerGo配置
	CrawlerGo CrawlerGoConfig `mapstructure:"crawlergo"`

	// GAU配置
	GAU GAUConfig `mapstructure:"gau"`
}

// RandomUserAgentConfig 随机User-Agent配置
type RandomUserAgentConfig struct {
	Enable                bool `mapstructure:"enable"`
	OverrideEngineSettings bool `mapstructure:"override_engine_settings"`
}

// CollyConfig Colly爬虫配置
type CollyConfig struct {
	Enable    bool                `mapstructure:"enable"`
	MaxDepth  int                 `mapstructure:"max_depth"`
	Async     bool                `mapstructure:"async"`
	MaxPages  int                 `mapstructure:"max_pages"`
	RateLimit CollyRateLimitConfig `mapstructure:"rate_limit"`
	Proxy     CollyProxyConfig     `mapstructure:"proxy"`
	Request   CollyRequestConfig   `mapstructure:"request"`
	Headers   map[string]string    `mapstructure:"headers"`
	Cookies   CollyCookieConfig    `mapstructure:"cookies"`
	Filters   CollyFiltersConfig   `mapstructure:"filters"`
	Cache     CollyCacheConfig     `mapstructure:"cache"`
	Debug     CollyDebugConfig     `mapstructure:"debug"`
}

// CollyRateLimitConfig Colly速率限制配置
type CollyRateLimitConfig struct {
	Enable         bool          `mapstructure:"enable"`
	Delay          time.Duration `mapstructure:"delay"`
	Parallelism    int           `mapstructure:"parallelism"`
	RandomizeDelay bool          `mapstructure:"randomize_delay"`
}

// CollyProxyConfig Colly代理配置
type CollyProxyConfig struct {
	Enable   bool   `mapstructure:"enable"`
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

// CollyRequestConfig Colly请求配置
type CollyRequestConfig struct {
	Timeout          time.Duration `mapstructure:"timeout"`
	UserAgent        string        `mapstructure:"user_agent"`
	RandomUserAgent  bool          `mapstructure:"random_user_agent"`
	FollowRedirects  bool          `mapstructure:"follow_redirects"`
	MaxRedirects     int           `mapstructure:"max_redirects"`
}

// CollyCookieConfig Colly Cookie配置
type CollyCookieConfig struct {
	Enable bool `mapstructure:"enable"`
	Jar    bool `mapstructure:"jar"`
}

// CollyFiltersConfig Colly过滤配置
type CollyFiltersConfig struct {
	AllowedDomains    []string `mapstructure:"allowed_domains"`
	DisallowedDomains []string `mapstructure:"disallowed_domains"`
	URLFilters        []string `mapstructure:"url_filters"`
}

// CollyCacheConfig Colly缓存配置
type CollyCacheConfig struct {
	Enable bool   `mapstructure:"enable"`
	Dir    string `mapstructure:"dir"`
}

// CollyDebugConfig Colly调试配置
type CollyDebugConfig struct {
	Enable       bool `mapstructure:"enable"`
	LogRequests  bool `mapstructure:"log_requests"`
	LogResponses bool `mapstructure:"log_responses"`
}

// PulseConfig Pulse爬虫配置
type PulseConfig struct {
	Enable      bool                        `mapstructure:"enable"`
	MaxDepth    int                         `mapstructure:"max_depth"`
	Concurrency int                         `mapstructure:"concurrency"`
	Timeout     int                         `mapstructure:"timeout"`
	RateLimit   int                         `mapstructure:"rate_limit"`
	Headers     map[string]string           `mapstructure:"headers"`
	Cookies     string                      `mapstructure:"cookies"`
	JavaScript  PulseJavaScriptConfig       `mapstructure:"javascript"`
	Forms       PulseFormsConfig            `mapstructure:"forms"`
	DirBrute    PulseDirectoryBruteConfig   `mapstructure:"directory_bruteforce"`
	Filters     PulseFiltersConfig          `mapstructure:"filters"`
}

// PulseJavaScriptConfig Pulse JavaScript配置
type PulseJavaScriptConfig struct {
	Enable  bool `mapstructure:"enable"`
	Timeout int  `mapstructure:"timeout"`
}

// PulseFormsConfig Pulse表单配置
type PulseFormsConfig struct {
	AutoFill bool `mapstructure:"auto_fill"`
	Submit   bool `mapstructure:"submit"`
}

// PulseDirectoryBruteConfig Pulse目录爆破配置
type PulseDirectoryBruteConfig struct {
	Enable   bool   `mapstructure:"enable"`
	Wordlist string `mapstructure:"wordlist"`
}

// PulseFiltersConfig Pulse过滤配置
type PulseFiltersConfig struct {
	Extensions  []string `mapstructure:"extensions"`
	StatusCodes []int    `mapstructure:"status_codes"`
}

// CrawlerGoConfig CrawlerGo爬虫配置
type CrawlerGoConfig struct {
	Enable          bool                      `mapstructure:"enable"`
	MaxDepth        int                       `mapstructure:"max_depth"`
	Concurrency     int                       `mapstructure:"concurrency"`
	Timeout         int                       `mapstructure:"timeout"`
	ChromePath      string                    `mapstructure:"chrome_path"`
	ChromeArgs      []string                  `mapstructure:"chrome_args"`
	IgnoreHTTPSErr  bool                      `mapstructure:"ignore_https_err"`
	MaxTabCount     int                       `mapstructure:"max_tab_count"`
	TabRunTimeout   int                       `mapstructure:"tab_run_timeout"`
	EventTrigger    CrawlerGoEventConfig      `mapstructure:"event_trigger"`
	FormSubmit      CrawlerGoFormConfig       `mapstructure:"form_submit"`
	Filters         CrawlerGoFiltersConfig    `mapstructure:"filters"`
	Output          CrawlerGoOutputConfig     `mapstructure:"output"`
}

// CrawlerGoEventConfig CrawlerGo事件触发配置
type CrawlerGoEventConfig struct {
	Enable       bool `mapstructure:"enable"`
	ClickButton  bool `mapstructure:"click_button"`
	ClickLink    bool `mapstructure:"click_link"`
	InputText    bool `mapstructure:"input_text"`
	SubmitForm   bool `mapstructure:"submit_form"`
}

// CrawlerGoFormConfig CrawlerGo表单配置
type CrawlerGoFormConfig struct {
	Enable       bool `mapstructure:"enable"`
	AutoFill     bool `mapstructure:"auto_fill"`
	Submit       bool `mapstructure:"submit"`
}

// CrawlerGoFiltersConfig CrawlerGo过滤配置
type CrawlerGoFiltersConfig struct {
	SkipExtensions []string `mapstructure:"skip_extensions"`
	AllowedDomains []string `mapstructure:"allowed_domains"`
	BlockedDomains []string `mapstructure:"blocked_domains"`
}

// CrawlerGoOutputConfig CrawlerGo输出配置
type CrawlerGoOutputConfig struct {
	JSON     bool `mapstructure:"json"`
	Verbose  bool `mapstructure:"verbose"`
	Silent   bool `mapstructure:"silent"`
}

// GAUConfig GAU配置
type GAUConfig struct {
	Enable              bool                `mapstructure:"enable"`
	MaxResultsPerSource int                 `mapstructure:"max_results_per_source"`
	Timeout             int                 `mapstructure:"timeout"`
	Threads             int                 `mapstructure:"threads"`
	Retries             int                 `mapstructure:"retries"`
	IncludeSubdomains   bool                `mapstructure:"include_subdomains"`
	RemoveParameters    bool                `mapstructure:"remove_parameters"`
	Verbose             bool                `mapstructure:"verbose"`
	Sources             []string            `mapstructure:"sources"`
	Proxy               GAUProxyConfig      `mapstructure:"proxy"`
	URLScan             GAUURLScanConfig    `mapstructure:"urlscan"`
	Filters             GAUFiltersConfig    `mapstructure:"filters"`
	BlacklistExtensions []string            `mapstructure:"blacklist_extensions"`
}

// GAUProxyConfig 代理配置
type GAUProxyConfig struct {
	Enable   bool   `mapstructure:"enable"`
	URL      string `mapstructure:"url"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
}

// GAUURLScanConfig URLScan.io配置
type GAUURLScanConfig struct {
	APIKey string `mapstructure:"api_key"`
}

// GAUFiltersConfig GAU过滤配置
type GAUFiltersConfig struct {
	MatchStatusCodes  []string `mapstructure:"match_status_codes"`
	FilterStatusCodes []string `mapstructure:"filter_status_codes"`
	MatchMimeTypes    []string `mapstructure:"match_mime_types"`
	FilterMimeTypes   []string `mapstructure:"filter_mime_types"`
	From              string   `mapstructure:"from"`
	To                string   `mapstructure:"to"`
}

// RateLimitConfig 速率限制配置
type RateLimitConfig struct {
	Enable bool          `mapstructure:"enable"`
	Delay  time.Duration `mapstructure:"delay"`
}

// XSSConfig XSS检测配置
type XSSConfig struct {
	Enable              bool    `mapstructure:"enable"`
	MinConfidenceScore  float64 `mapstructure:"min_confidence_score"`
	MaxPayloads         int     `mapstructure:"max_payloads"`
	ParamFuzzing        bool    `mapstructure:"param_fuzzing"`
	ASTAnalysis         bool    `mapstructure:"ast_analysis"`
	ContextAware        bool    `mapstructure:"context_aware"`
}

// DirScanConfig 目录扫描配置
type DirScanConfig struct {
	Enable      bool     `mapstructure:"enable"`
	Extensions  []string `mapstructure:"extensions"`
	Wordlists   []string `mapstructure:"wordlists"`
	Concurrency int      `mapstructure:"concurrency"`
}

// JSAnalyzerConfig JavaScript分析配置
type JSAnalyzerConfig struct {
	Enable           bool `mapstructure:"enable"`
	SensitiveInfo    bool `mapstructure:"sensitive_info"`
	APIEndpoints     bool `mapstructure:"api_endpoints"`
	UnauthorizedTest bool `mapstructure:"unauthorized_test"`
}

// ProxyConfig 代理配置
type ProxyConfig struct {
	Enable bool   `mapstructure:"enable"`
	Port   int    `mapstructure:"port"`
	Host   string `mapstructure:"host"`
}

// ReporterConfig 报告配置
type ReporterConfig struct {
	Enable   bool     `mapstructure:"enable"`
	Formats  []string `mapstructure:"formats"`
	Template string   `mapstructure:"template"`
}

var GlobalConfig *Config

// LoadConfig 加载配置文件
func LoadConfig(configPath string) (*Config, error) {
	config := &Config{}
	
	// 设置默认配置
	setDefaults()
	
	// 如果指定了配置文件路径
	if configPath != "" {
		viper.SetConfigFile(configPath)
	} else {
		// 查找配置文件
		viper.SetConfigName("config")
		viper.SetConfigType("yaml")
		viper.AddConfigPath(".")
		viper.AddConfigPath("./config")
		viper.AddConfigPath("$HOME/.pulsex")
	}
	
	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); !ok {
			return nil, fmt.Errorf("读取配置文件失败: %v", err)
		}
		// 配置文件不存在，使用默认配置
	}
	
	// 解析配置
	if err := viper.Unmarshal(config); err != nil {
		return nil, fmt.Errorf("解析配置失败: %v", err)
	}
	
	// 创建输出目录
	if err := os.MkdirAll(config.OutputDir, 0755); err != nil {
		return nil, fmt.Errorf("创建输出目录失败: %v", err)
	}
	
	GlobalConfig = config
	return config, nil
}

// setDefaults 设置默认配置
func setDefaults() {
	// 基础配置
	viper.SetDefault("debug", false)
	viper.SetDefault("log_level", "info")
	viper.SetDefault("output_dir", "./reports")
	
	// 爬虫配置
	viper.SetDefault("crawler.concurrency", 10)
	viper.SetDefault("crawler.timeout", "30s")
	viper.SetDefault("crawler.user_agent", "Pulsex/1.0.0")
	viper.SetDefault("crawler.max_depth", 3)

	// 全局自定义请求头配置
	viper.SetDefault("crawler.headers", map[string]string{})
	viper.SetDefault("crawler.cookies", "")

	// 全局随机User-Agent配置
	viper.SetDefault("crawler.random_user_agent.enable", true)
	viper.SetDefault("crawler.random_user_agent.override_engine_settings", true)
	
	// Colly配置
	viper.SetDefault("crawler.colly.enable", true)
	viper.SetDefault("crawler.colly.max_depth", 3)
	viper.SetDefault("crawler.colly.async", true)
	viper.SetDefault("crawler.colly.max_pages", 1000)

	// Colly速率限制
	viper.SetDefault("crawler.colly.rate_limit.enable", true)
	viper.SetDefault("crawler.colly.rate_limit.delay", "1s")
	viper.SetDefault("crawler.colly.rate_limit.parallelism", 2)
	viper.SetDefault("crawler.colly.rate_limit.randomize_delay", true)

	// Colly代理
	viper.SetDefault("crawler.colly.proxy.enable", false)
	viper.SetDefault("crawler.colly.proxy.url", "")
	viper.SetDefault("crawler.colly.proxy.username", "")
	viper.SetDefault("crawler.colly.proxy.password", "")

	// Colly请求
	viper.SetDefault("crawler.colly.request.timeout", "30s")
	viper.SetDefault("crawler.colly.request.user_agent", "")  // 空字符串表示使用随机用户代理
	viper.SetDefault("crawler.colly.request.random_user_agent", true)
	viper.SetDefault("crawler.colly.request.follow_redirects", true)
	viper.SetDefault("crawler.colly.request.max_redirects", 10)

	// Colly Cookie
	viper.SetDefault("crawler.colly.cookies.enable", true)
	viper.SetDefault("crawler.colly.cookies.jar", true)

	// Colly过滤
	viper.SetDefault("crawler.colly.filters.allowed_domains", []string{})
	viper.SetDefault("crawler.colly.filters.disallowed_domains", []string{})
	viper.SetDefault("crawler.colly.filters.url_filters", []string{})

	// Colly缓存
	viper.SetDefault("crawler.colly.cache.enable", false)
	viper.SetDefault("crawler.colly.cache.dir", "./cache/colly")

	// Colly调试
	viper.SetDefault("crawler.colly.debug.enable", false)
	viper.SetDefault("crawler.colly.debug.log_requests", true)
	viper.SetDefault("crawler.colly.debug.log_responses", false)

	// Pulse配置
	viper.SetDefault("crawler.pulse.enable", true)
	viper.SetDefault("crawler.pulse.max_depth", 5)
	viper.SetDefault("crawler.pulse.concurrency", 10)
	viper.SetDefault("crawler.pulse.timeout", 30)
	viper.SetDefault("crawler.pulse.rate_limit", 150)
	viper.SetDefault("crawler.pulse.headers", map[string]string{})
	viper.SetDefault("crawler.pulse.cookies", "")

	// Pulse JavaScript
	viper.SetDefault("crawler.pulse.javascript.enable", true)
	viper.SetDefault("crawler.pulse.javascript.timeout", 10)

	// Pulse表单
	viper.SetDefault("crawler.pulse.forms.auto_fill", true)
	viper.SetDefault("crawler.pulse.forms.submit", false)

	// Pulse目录爆破
	viper.SetDefault("crawler.pulse.directory_bruteforce.enable", true)
	viper.SetDefault("crawler.pulse.directory_bruteforce.wordlist", "common")

	// Pulse过滤
	viper.SetDefault("crawler.pulse.filters.extensions", []string{"php", "asp", "aspx", "jsp", "do", "action"})
	viper.SetDefault("crawler.pulse.filters.status_codes", []int{200, 301, 302, 403, 500})
	
	// GAU配置
	viper.SetDefault("crawler.gau.enable", true)
	viper.SetDefault("crawler.gau.max_results_per_source", 1000)
	viper.SetDefault("crawler.gau.timeout", 60)
	viper.SetDefault("crawler.gau.threads", 5)
	viper.SetDefault("crawler.gau.retries", 3)
	viper.SetDefault("crawler.gau.include_subdomains", false)
	viper.SetDefault("crawler.gau.remove_parameters", true)
	viper.SetDefault("crawler.gau.verbose", false)
	viper.SetDefault("crawler.gau.sources", []string{"wayback", "commoncrawl", "otx", "urlscan", "grep"})
	viper.SetDefault("crawler.gau.proxy.enable", false)
	viper.SetDefault("crawler.gau.proxy.url", "")
	viper.SetDefault("crawler.gau.proxy.username", "")
	viper.SetDefault("crawler.gau.proxy.password", "")
	viper.SetDefault("crawler.gau.urlscan.api_key", "")
	viper.SetDefault("crawler.gau.filters.match_status_codes", []string{})
	viper.SetDefault("crawler.gau.filters.filter_status_codes", []string{})
	viper.SetDefault("crawler.gau.filters.match_mime_types", []string{})
	viper.SetDefault("crawler.gau.filters.filter_mime_types", []string{"image/png", "image/jpg", "image/jpeg", "image/gif", "image/svg+xml", "text/css", "application/javascript"})
	viper.SetDefault("crawler.gau.filters.from", "")
	viper.SetDefault("crawler.gau.filters.to", "")
	viper.SetDefault("crawler.gau.blacklist_extensions", []string{"jpg", "jpeg", "png", "gif", "bmp", "svg", "ico", "css", "js", "woff", "woff2", "ttf", "eot", "pdf", "doc", "docx", "zip", "rar", "tar", "gz", "mp3", "mp4", "avi"})
	
	// XSS配置
	viper.SetDefault("xss.enable", true)
	viper.SetDefault("xss.min_confidence_score", 0.7)
	viper.SetDefault("xss.max_payloads", 50)
	viper.SetDefault("xss.param_fuzzing", true)
	viper.SetDefault("xss.ast_analysis", true)
	viper.SetDefault("xss.context_aware", true)
	
	// 目录扫描配置
	viper.SetDefault("dirscan.enable", true)
	viper.SetDefault("dirscan.extensions", []string{"php", "asp", "aspx", "jsp", "js", "html"})
	viper.SetDefault("dirscan.wordlists", []string{"common_dirs.txt", "xss_dirs.txt"})
	viper.SetDefault("dirscan.concurrency", 20)
	
	// JavaScript分析配置
	viper.SetDefault("jsanalyzer.enable", true)
	viper.SetDefault("jsanalyzer.sensitive_info", true)
	viper.SetDefault("jsanalyzer.api_endpoints", true)
	viper.SetDefault("jsanalyzer.unauthorized_test", true)
	
	// 代理配置
	viper.SetDefault("proxy.enable", false)
	viper.SetDefault("proxy.port", 8080)
	viper.SetDefault("proxy.host", "127.0.0.1")
	
	// 报告配置
	viper.SetDefault("reporter.enable", true)
	viper.SetDefault("reporter.formats", []string{"markdown", "html"})
	viper.SetDefault("reporter.template", "default")
}

// GetConfigDir 获取配置目录
func GetConfigDir() string {
	configDir := "./config"
	if _, err := os.Stat(configDir); os.IsNotExist(err) {
		os.MkdirAll(configDir, 0755)
	}
	return configDir
}

// GetDictPath 获取字典文件路径
func GetDictPath(filename string) string {
	return filepath.Join(GetConfigDir(), "dicts", filename)
}

// GetPayloadPath 获取payload文件路径
func GetPayloadPath(filename string) string {
	return filepath.Join(GetConfigDir(), "payloads", filename)
}

// GetRulePath 获取规则文件路径
func GetRulePath(filename string) string {
	return filepath.Join(GetConfigDir(), "rules", filename)
}
