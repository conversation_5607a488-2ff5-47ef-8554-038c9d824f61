# Pulsex v1.0.0 - 自动化XSS扫描框架

## 📋 项目简介

Pulsex 是一款基于 Go 语言开发的高性能、全自动 XSS 漏洞扫描框架。集成了多种爬虫技术、AST 语法验证、智能探测等先进技术，可以高效准确地发现 Web 应用中的跨站脚本（XSS）漏洞。

## ✨ 核心特性

### 🕷️ 多引擎爬虫系统
- **Colly 爬虫**: 高性能网页抓取 + 智能目录遍历
- **Katana 爬虫**: 深度 JavaScript 分析和表单处理
- **GAU 引擎**: 历史 URL 数据挖掘（Wayback、CommonCrawl、OTX、URLScan）

```

1、在构建完成测试时禁止使用外网测试域名例如example.com等等，需要测试的话就在本地搭建一个新建靶场测试
2、不要对没有实现的扫描逻辑实现虚拟数据模拟，禁止出现任何模拟数据
3、xss、sqli逻辑可以参考跟录下sqli
sqli2
sqli3
XS
的py实现的逻辑

4、敏感信息检测匹配查考https://github.com/gh0stkey/HaE
https://github.com/gh0stkey/HaE/blob/master/src/main/resources/rules/Rules.yml
5、由于项目过于复杂请细分成多个小任务构建，

6、6. 1. **GAU历史资产采集**： 
    异步启动GAU，从多个数据源（Wayback Machine, Common Crawl, OTX, URLScan等）获取目标域名的历史URL和JS文件路径。 
    输出：URL列表（包括js文件、api路径等） 
 6.2. **Colly轻量爬虫**： 
    同时启动Colly，进行快速静态爬取，特点： 
      - 深度优先（Depth=3） 
      - 只处理HTML和JS文件 
      - 提取所有链接（a标签、iframe、script等）以及页面中的API端点（通过正则匹配） 
    输出：当前站点的静态资源链接、API端点、JS文件路径 
 6.3. **资产合并与去重**： 
    将GAU和Colly的输出合并，并进行去重（基于URL标准化：去除参数、锚点，统一小写等）。 
    去重后，将资产分为两类： 
      - 普通链接（HTML页面） 
      - 高价值目标（JS文件、API路径、带参数的动态页面） 
 6.4. **Katana深度爬取**： 
    将合并后的资产作为输入，启动Katana进行深度爬取： 
      - 对普通链接：进行深度爬取（depth=5），并开启目录爆破（使用内置字典） 
      - 对高价值目标：开启JS渲染（-js-crawl）和表单自动提交（-form-fill） 
    输出：所有发现的链接、API、敏感信息等 
 6.5. **结果后处理**： 
      - 敏感信息扫描：对Katana输出的所有文本内容（HTML/JS）进行自定义正则匹配（API密钥、邮箱等） 
      - 未授权访问检测：对发现的API进行HTTP方法测试（GET,POST,PUT,DELETE）并检查响应状态码 
      - 漏洞检测：对参数化URL进行XSS/SQL注入测试（注意：使用无害化Payload）

爬虫对应采用以下项目：
https://Pulsex/lib
https://Pulsex/lib
https://github.com/projectdiscovery/katana



先为我实现基础框架和方案
```



### 🔍 智能检测技术
- **AST 语法验证**: 基于抽象语法树的精确上下文分析
- **智能无害探测**: 减少误报的渐进式检测策略
- **上下文感知**: 精确识别 HTML、JavaScript、CSS 等执行环境

### 🛠️ 高级功能
- **目录遍历扫描**: 智能字典选择 + 技术栈识别
- **JavaScript 分析**: 敏感信息提取 + API 端点发现
- **实时报告生成**: Markdown/HTML多格式输出
- **参数模糊测试**: 智能参数发现和反射验证
- **敏感信息检测**: 通过正则匹配爬取的响应体中的敏感信息
- **未授权检测**: 通过对js文件中的接口进行拼接发送GET和POST，根据响应判断是否存在未授权（过滤高危操作风险接口增删改）
- **被动扫描**: 解决cookie token问题

### 🚀 性能优化
- **并发扫描**: 可配置并发数，充分利用系统资源
- **智能去重**: 避免重复扫描相同目标
- **缓存机制**: 提升重复检测效率
- **超时控制**: 防止长时间阻塞

## 📦 安装配置

### 系统要求
- Go 1.19 或更高版本
- Windows/Linux/macOS 系统
- 至少 2GB 可用内存

## 📦 基础演示
```bash
.\Pulsex.exe crawl -u https://example.com
```
爬虫+xss
<img width="1440" height="593" alt="image" src="./assets/be75855d-f74f-4856-ab0a-2ca5ef591577.png" />

```bash
.\Pulsex.exe -h
```
参数帮助
<img width="1029" height="252" alt="image" src="./assets/006409a5-bc43-44fc-846d-3ca6a2fed83b.png" />

```bash
.\Pulsex.exe xss -u https://example.com
```
单独运行xss
<img width="1010" height="333" alt="image" src="./assets/6fc6e267-397c-45a6-ba84-6513051f49f8.png" />

报告生成
<img width="829" height="472" alt="image" src="./assets/7aea92e8-ecf1-4fcf-a75a-dcf159bd664a.png" />

```bash
.\Pulsex.exe dir -u https://example.com -e php
```
单独运行目录遍历
<img width="1442" height="740" alt="image" src="./assets/5b6e7f03-325a-4bc9-bbf9-8522d7318b4a.png" />

报告生成
<img width="1367" height="947" alt="image" src="./assets/f81a3fd6-258e-4400-a33a-d18526ec150c.png" />

```bash
.\Pulsex.exe js -u https://example.com
```
单独运行js分析
<img width="1464" height="907" alt="image" src="https://github.com/user-attachments/assets/bbfe8144-50d6-419f-a5cf-6edf44708c6a" />
<img width="1317" height="942" alt="image" src="./assets/82109193-e24d-4e0a-8523-094dabbe025e.png" />



### 注意
```bash
- 本程序为go编译的二进制,不要双击运行会闪退,命令行运行 Pulsex.exe -h (win)
```



## 🚀 快速开始

### 基础扫描
```bash
# 扫描单个URL
./Pulsex crawl -u https://example.com (linux)
Pulsex.exe crawl -u https://example.com (win)

# 从文件批量扫描
./Pulsex crawl -f targets.txt (linux)
Pulsex.exe crawl -f targets.txt(win)
```

### 被动扫描
```bash
Pulsex.exe proxy -p 9090 (win)(不指定-p 则默认为8080端口)
```


### 独立模块使用
```bash
# 纯XSS扫描（无爬虫）
./Pulsex xss -u https://example.com/search?q=test
./Pulsex xss -f targets.txt

# 目录遍历扫描
./Pulsex dir -u https://example.com -e php(linux) / -e 指定语言类型
./Pulsex dir -f targets.txt -e all (linux) / all 表示默认不指定全量
Pulsex.exe dir -u https://0zqq.com -e php(win)

# JavaScript分析
./Pulsex js -u https://example.com/app.js
./Pulsex js -f targets.tx
```

### 2. 代码审计辅助
```bash
# 分析JavaScript文件中的敏感信息
./Pulsex js -u https://app.com/assets/app.js 

# 目录遍历发现隐藏文件
./Pulsex dir -u https://app.com -e all
```

### 3. 持续安全检测
```bash
# 定期扫描脚本
#!/bin/bash
./Pulsex crawl -f production_urls.txt
# 结合CI/CD流程进行自动化检测
```

### 4. 漏洞验证
```bash
# 针对特定参数验证XSS
./Pulsex xss -u "https://site.com/search?q=test&category=all"
```

### 1. 性能优化
```yaml
# 高性能配置示例
crawler:
  concurrency: 1000          # 提高并发数
colly:
  rate_limit:
    enable: false            # 关闭速率限制
gau:
  max_results_per_source: 5000 # 增加结果数量
```

### 2. 精度调优
```yaml
# 高精度配置
xss:
  min_confidence_score: 0.8   # 提高置信度阈值
param_fuzzing:
  enable: true                # 启用fuzz

```

### 3. 自定义字典
```bash
# 编辑参数字典
vim dicts/fuzz.txt
# 添加业务相关参数
echo "user_input" >> dicts/fuzz.txt
echo "search_term" >> dicts/fuzz.txt
```

### 4. 结果分析
```bash
# 提取高危漏洞
grep "Critical\|High" reports/xss_realtime_*.md

# 统计漏洞数量
grep "🚨 漏洞 #" reports/xss_realtime_*.md | wc -l

# 查看特定类型漏洞
grep "javascript" reports/xss_realtime_*.md
```

## ⚠️ 注意事项

### 法律合规
- ✅ 仅对授权目标进行扫描
- ✅ 遵守相关法律法规
- ❌ 禁止用于未授权渗透测试
- ❌ 禁止用于恶意攻击

### 技术限制
- 部分 WAF 可能阻拦扫描请求
- 复杂 JavaScript 应用可能需要人工验证
- 网络环境可能影响爬虫效果
- 大型站点建议分批扫描

### 性能建议
- 合理设置并发数避免目标服务器过载
- 使用代理池分散请求来源
- 定期清理临时文件和缓存
- 监控系统资源使用情况


📝 更新日志



🛠️优化线程池
🛠️优化cookie注入

🔥新增被动扫描模式,(测试阶段)
🔥新增端口扫描模式(测试阶段)
🔥新增数据清洗功能
🔥优化xss ast抽象树分析



🗂️目录遍历更新:
搭配XSS以及单独运行模式,字典自动选择,使用xss_dirs.txt
发现的路径自动加入XSS扫描队列,使用common_dirs.txt
专注于目录遍历漏洞发现并生成生成完整HTML报告
🛠️ xss引擎重大更新降低误报
🎯 colly爬虫优化,将会取更多url参数



🛠️ 修复探测证据：只取核心证据
🔥 优化 JavaScript 引号逃逸检测
🎯 不同参数 XSS 去重避免报告重复



🚀 整体调试编译发布内测版
📋 持续收集用户意见进行优化



🗂️ 优化目录遍历功能



🎯 新增 JavaScript 深度分析器



🧠 XSS 引擎 AST 语法全面优化



🔧 XSS 引擎架构全量升级重构



🚀 爬虫框架性能深度优化



🎯 参数提取器集成 Fuzz 测试功能



📝 新增调试日志配置
🧠 XSS 引擎智能探测升级
📊 报告输出优化



🔧 爬虫输出智能管理
⚡ GAU 功能重构
📋 配置文件统一优化



🎯 加入目录遍历功能
🗂️ 支持独立目录扫描
📚 字典检测优化



🌟 置信度阈值系统重大更新
🔧 支持多场景配置



🌟 XSS 引擎工作流程全新升级
🔍 多维度检测机制



⚡ 评分规则算法更新
📊 报告输出详细化



🎯 JS AST 分析功能上线
🔍 XSS 引擎独立运行



🌟 XSS 引擎语义分析重大升级
🛡️ 无害探测机制



🚀 四大爬虫框架完成
🔍 XSS 引擎扫描上线
📊 实时报告生成



---

**⚡ Pulsex - 让XSS扫描更智能、更高效！**