{"target": "http://192.168.11.1:8888/", "scan_time": "2025-07-30T17:00:42.6846079+08:00", "duration": "1.6018034s", "version": "1.0.0", "summary": {"total_urls": 48, "total_js_files": 4, "total_parameters": 0, "total_forms": 2, "total_cookies": 2, "total_subdomains": 0, "gau_urls": 0, "colly_urls": 0, "pulse_urls": 48, "crawlergo_urls": 0}, "crawl_results": {"urls": [{"url": "http://192.168.11.1:8888/", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/sqli/jdbc", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/index/sqli/mybatis", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/index/upload", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "MEDIUM"}, {"url": "http://192.168.11.1:8888/index/traversal", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jwt", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/captcha", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xss", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xss/store", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/ssrf", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/rce", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/deserialize", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/spel", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xxe", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/idor", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/unauth", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/ssti", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jndi", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xstream", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/fastjson", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jackson", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/log4j", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/redirect", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/actuator", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xff", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/swagger", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/cors", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/dos", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xpath", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/csv", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/csrf", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/vul/order?field=id&sort=desc,abs(111111)", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/order?field=id", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/id/1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/query?user=admin", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/js/jquery-3.6.0.min.js", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/bootstrap-4.6.0.min.js", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/codemirror.js", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/groovy.js", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/vul/search", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul1?id=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul2?id=2%20or%201=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul3?id=2", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe2?id=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe1?id=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe3?id=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe4?id=1", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/sqli/jdbc", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/index/sqli/mybatis", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/index/upload", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "MEDIUM"}, {"url": "http://192.168.11.1:8888/index/traversal", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jwt", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/captcha", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xss", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xss/store", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/ssrf", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/rce", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/deserialize", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/spel", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xxe", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/idor", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/unauth", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/ssti", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jndi", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xstream", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/fastjson", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/jackson", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/log4j", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/redirect", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/actuator", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xff", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/swagger", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/cors", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/dos", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/xpath", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/csv", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/index/csrf", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/vul/order?field=id&sort=desc,abs(111111)", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/order?field=id", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/id/1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/safe/query?user=admin", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/js/jquery-3.6.0.min.js", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/bootstrap-4.6.0.min.js", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/codemirror.js", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/js/groovy.js", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/SQLI/MyBatis/vul/search", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul1?id=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul2?id=2%20or%201=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/vul3?id=2", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe2?id=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe1?id=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe3?id=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/SQLI/JDBC/safe4?id=1", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}], "js_files": [{"url": "http://192.168.11.1:8888/js/jquery-3.6.0.min.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/bootstrap-4.6.0.min.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/codemirror.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/groovy.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/jquery-3.6.0.min.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/bootstrap-4.6.0.min.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/codemirror.js", "size": 0, "endpoints": [], "secrets": []}, {"url": "http://192.168.11.1:8888/js/groovy.js", "size": 0, "endpoints": [], "secrets": []}], "parameters": [], "forms": [{"action": "/user/login;jsessionid=D36C7855BDE7730E208BD8541A03E895", "method": "POST", "fields": {"captcha": "text", "password": "password", "username": "text"}, "source": "<PERSON>ly"}, {"action": "/user/login;jsessionid=D36C7855BDE7730E208BD8541A03E895", "method": "POST", "fields": {"captcha": "text", "password": "password", "username": "text"}, "source": "<PERSON>rged"}], "cookies": [{"name": "JSESSIONID", "value": "D36C7855BDE7730E208BD8541A03E895", "domain": "192.168.11.1:8888", "path": "/", "secure": false, "httponly": false}, {"name": "JSESSIONID", "value": "D36C7855BDE7730E208BD8541A03E895", "domain": "192.168.11.1:8888", "path": "/", "secure": false, "httponly": false}], "subdomains": [], "technologies": []}, "xss_results": [], "vulnerabilities": {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0, "total": 0}, "risk_assessment": {"overall_risk": "CRITICAL", "risk_score": 580, "risk_factors": ["发现管理员路径", "发现管理员路径"], "recommendations": ["限制管理员路径的访问权限", "限制管理员路径的访问权限"], "categories": {"中风险URL": 2, "高风险URL": 28}}}