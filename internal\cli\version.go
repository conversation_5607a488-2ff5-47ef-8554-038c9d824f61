package cli

import (
	"fmt"
	"runtime"
	"time"

	"github.com/spf13/cobra"
)

var versionCmd = &cobra.Command{
	Use:   "version",
	Short: "📋 显示版本信息",
	Long:  "📋 显示Pulsex的详细版本信息和构建信息",
	PreRun: func(cmd *cobra.Command, args []string) {
		// 版本命令不需要配置文件，跳过配置加载
	},
	Run: func(cmd *cobra.Command, args []string) {
		showVersion()
	},
}

func showVersion() {
	buildTime := time.Now().Format("2006-01-02 15:04:05")
	
	fmt.Printf(`
%s
    ____        __
   / __ \__  __/ /___ ___  _  __
  / /_/ / / / / / __ `+"`"+`__ \| |/_/
 / ____/ /_/ / / /_/ / / //>  <
/_/    \__,_/_/\__,_/_/ /_/_/|_|

%s v%s - 专业XSS漏洞扫描框架

%s 版本信息:
  版本号:     %s
  Go版本:     %s
  操作系统:   %s
  架构:       %s
  构建时间:   %s

%s 功能模块:
  🕷️  多引擎爬虫系统 (Colly, Pulse, GAU, CrawlerGo)
  🔍 智能XSS检测引擎 (AST语法验证 + 上下文感知)
  📁 目录遍历扫描 (智能字典 + 状态码分析)
  📜 JavaScript深度分析 (DOM-XSS + 反射型检测)
  🔄 被动扫描代理 (流量拦截 + 实时分析)
  📊 专业报告生成 (HTML + JSON + PDF)

%s 开发信息:
  项目地址:   https://github.com/your-org/pulsex
  文档地址:   https://pulsex.readthedocs.io
  问题反馈:   https://github.com/your-org/pulsex/issues
  许可证:     MIT License

%s 使用示例:
  pulsex crawl -u https://example.com        # 单目标扫描
  pulsex crawl -f targets.txt               # 批量目标扫描
  pulsex crawl -u target.com -e all         # 使用所有引擎
  pulsex proxy -p 8080                      # 启动被动代理

更多帮助: pulsex --help
`,
		cyan(""),
		cyan(AppName), cyan(Version),
		blueBg(" 系统 "),
		green(Version),
		green(runtime.Version()),
		green(runtime.GOOS),
		green(runtime.GOARCH),
		green(buildTime),
		blueBg(" 功能 "),
		blueBg(" 项目 "),
		blueBg(" 示例 "),
	)
}

func init() {
	// 版本命令不需要额外的标志
}
