package wayback

import (
	"context"
	"errors"
	"fmt"
	"net"
	"strings"

	jsoniter "github.com/json-iterator/go"
	"github.com/lc/gau/v2/pkg/httpclient"
	"github.com/lc/gau/v2/pkg/providers"
	"github.com/sirupsen/logrus"
)

const (
	Name = "wayback"
)

// verify interface compliance
var _ providers.Provider = (*Client)(nil)

// Client is the structure that holds the WaybackFilters and the Client's configuration
type Client struct {
	filters providers.Filters
	config  *providers.Config
}

func New(config *providers.Config, filters providers.Filters) *Client {
	return &Client{filters, config}
}

func (c *Client) Name() string {
	return Name
}

// waybackResult holds the response from the wayback API
type waybackResult [][]string

// isIPAddress 检查字符串是否为IP地址
func isIPAddress(domain string) bool {
	return net.ParseIP(domain) != nil
}

// Fe<PERSON> fetches all urls for a given domain and sends them to a channel.
// It returns an error should one occur.
func (c *Client) Fetch(ctx context.Context, domain string, results chan string) error {
	// 检查是否为IP地址，Wayback Machine不支持IP地址查询
	if isIPAddress(domain) {
		logrus.WithFields(logrus.Fields{"provider": Name}).Infof("跳过IP地址 %s，Wayback Machine仅支持域名查询", domain)
		return nil
	}

	for page := uint(1); ; page++ {
		select {
		case <-ctx.Done():
			return nil
		default:
			logrus.WithFields(logrus.Fields{"provider": Name, "page": page}).Infof("fetching %s", domain)
			apiURL := c.formatURL(domain, page)
			fmt.Println(apiURL)
			// make HTTP request with shorter timeout for wayback
			resp, err := httpclient.MakeRequest(c.config.Client, apiURL, 2, 15) // 减少重试次数和超时时间
			if err != nil {
				if errors.Is(err, httpclient.ErrBadRequest) {
					logrus.WithFields(logrus.Fields{"provider": Name, "page": page}).Warnf("no more results for %s", domain)
					return nil
				}
				// 对于网络超时错误，记录警告但不返回错误，继续尝试下一页
				if errors.Is(err, context.DeadlineExceeded) ||
				   strings.Contains(err.Error(), "timeout") ||
				   strings.Contains(err.Error(), "connectex") {
					logrus.WithFields(logrus.Fields{"provider": Name, "page": page}).Warnf("%s - network timeout, skipping page %d: %v", domain, page, err)
					continue // 跳过这一页，继续下一页
				}
				return fmt.Errorf("failed to fetch wayback results page %d: %s", page, err)
			}
			var result waybackResult
			if err = jsoniter.Unmarshal(resp, &result); err != nil {
				return fmt.Errorf("failed to decode wayback results for page %d: %s", page, err)
			}

			// check if there's results, wayback's pagination response
			// is not always correct when using a filter
			if len(result) == 0 {
				break
			}

			// output results
			// Slicing as [1:] to skip first result by default
			for _, entry := range result[1:] {
				results <- entry[0]
			}
		}
	}
}

// formatUrl returns a formatted URL for the Wayback API
func (c *Client) formatURL(domain string, page uint) string {
	if c.config.IncludeSubdomains {
		domain = "*." + domain
	}
	filterParams := c.filters.GetParameters(true)
	return fmt.Sprintf(
		"https://web.archive.org/cdx/search/cdx?url=%s/*&output=json&collapse=urlkey&fl=original&pageSize=100&page=%d",
		domain, page,
	) + filterParams
}
