# 敏感信息检测规则配置
# 基于 HaE 项目规则改编

rules:
  # API密钥类
  - name: "AWS Access Key"
    pattern: "AKIA[0-9A-Z]{16}"
    severity: "High"
    description: "AWS访问密钥"
    
  - name: "AWS Secret Key"
    pattern: "[A-Za-z0-9/+=]{40}"
    severity: "High"
    description: "AWS秘密密钥"
    
  - name: "Google API Key"
    pattern: "AIza[0-9A-Za-z\\-_]{35}"
    severity: "High"
    description: "Google API密钥"
    
  - name: "GitHub Token"
    pattern: "ghp_[0-9a-zA-Z]{36}"
    severity: "High"
    description: "GitHub个人访问令牌"
    
  - name: "Slack Token"
    pattern: "xox[baprs]-([0-9a-zA-Z]{10,48})?"
    severity: "High"
    description: "Slack令牌"
    
  - name: "JWT Token"
    pattern: "eyJ[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*\\.[A-Za-z0-9_-]*"
    severity: "Medium"
    description: "JWT令牌"
    
  # 数据库连接字符串
  - name: "MySQL Connection"
    pattern: "mysql://[^\\s]+"
    severity: "High"
    description: "MySQL连接字符串"
    
  - name: "PostgreSQL Connection"
    pattern: "postgres://[^\\s]+"
    severity: "High"
    description: "PostgreSQL连接字符串"
    
  - name: "MongoDB Connection"
    pattern: "mongodb://[^\\s]+"
    severity: "High"
    description: "MongoDB连接字符串"
    
  - name: "Redis Connection"
    pattern: "redis://[^\\s]+"
    severity: "Medium"
    description: "Redis连接字符串"
    
  # 邮箱地址
  - name: "Email Address"
    pattern: "[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}"
    severity: "Info"
    description: "邮箱地址"
    
  # IP地址
  - name: "Private IP"
    pattern: "(10\\.|172\\.(1[6-9]|2[0-9]|3[01])\\.|192\\.168\\.)[0-9]{1,3}\\.[0-9]{1,3}"
    severity: "Info"
    description: "私有IP地址"
    
  - name: "Public IP"
    pattern: "(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)"
    severity: "Info"
    description: "公网IP地址"
    
  # 密码相关
  - name: "Password Field"
    pattern: "(password|passwd|pwd)\\s*[:=]\\s*['\"][^'\"]{6,}['\"]"
    severity: "High"
    description: "密码字段"
    case_sensitive: false
    
  - name: "API Key Field"
    pattern: "(api_key|apikey|access_key|secret_key)\\s*[:=]\\s*['\"][^'\"]{10,}['\"]"
    severity: "High"
    description: "API密钥字段"
    case_sensitive: false
    
  # 云服务密钥
  - name: "Alibaba Cloud AccessKey"
    pattern: "LTAI[A-Za-z0-9]{12,20}"
    severity: "High"
    description: "阿里云AccessKey"
    
  - name: "Tencent Cloud SecretId"
    pattern: "AKID[A-Za-z0-9]{13,40}"
    severity: "High"
    description: "腾讯云SecretId"
    
  - name: "Baidu Cloud API Key"
    pattern: "[0-9a-fA-F]{32}"
    severity: "Medium"
    description: "百度云API密钥"
    
  # 第三方服务
  - name: "WeChat AppSecret"
    pattern: "[0-9a-f]{32}"
    severity: "High"
    description: "微信AppSecret"
    
  - name: "Alipay Private Key"
    pattern: "-----BEGIN RSA PRIVATE KEY-----[\\s\\S]*-----END RSA PRIVATE KEY-----"
    severity: "Critical"
    description: "支付宝私钥"
    
  # 敏感路径
  - name: "Config File Path"
    pattern: "(/etc/|/config/|/conf/)[^\\s]*\\.(conf|config|ini|yaml|yml|json|xml)"
    severity: "Medium"
    description: "配置文件路径"
    
  - name: "Log File Path"
    pattern: "(/var/log/|/logs/)[^\\s]*\\.(log|txt)"
    severity: "Low"
    description: "日志文件路径"
    
  # 敏感URL
  - name: "Admin URL"
    pattern: "https?://[^\\s]*/admin[^\\s]*"
    severity: "Medium"
    description: "管理后台URL"
    
  - name: "API Endpoint"
    pattern: "https?://[^\\s]*/api/[^\\s]*"
    severity: "Info"
    description: "API端点"
    
  # 手机号码
  - name: "Chinese Mobile"
    pattern: "1[3-9][0-9]{9}"
    severity: "Info"
    description: "中国手机号码"
    
  # 身份证号
  - name: "Chinese ID Card"
    pattern: "[1-9][0-9]{5}(18|19|20)[0-9]{2}(0[1-9]|1[0-2])(0[1-9]|[12][0-9]|3[01])[0-9]{3}[0-9Xx]"
    severity: "High"
    description: "中国身份证号码"
    
  # 银行卡号
  - name: "Bank Card"
    pattern: "[0-9]{16,19}"
    severity: "High"
    description: "银行卡号"
    
  # 敏感关键词
  - name: "Sensitive Keywords"
    pattern: "(secret|password|token|key|credential|auth|login|admin|root|debug|test|dev|staging)"
    severity: "Low"
    description: "敏感关键词"
    case_sensitive: false
    
  # 版本信息
  - name: "Version Info"
    pattern: "(version|ver|v)[\\s:=]+[0-9]+\\.[0-9]+[\\.[0-9]+]*"
    severity: "Info"
    description: "版本信息"
    case_sensitive: false
    
  # 注释中的敏感信息
  - name: "TODO Comments"
    pattern: "(TODO|FIXME|HACK|XXX|BUG)\\s*:?\\s*[^\\n\\r]*"
    severity: "Info"
    description: "待办注释"
    case_sensitive: false

# 文件类型配置
file_types:
  javascript:
    - ".js"
    - ".jsx"
    - ".ts"
    - ".tsx"
  config:
    - ".json"
    - ".yml"
    - ".yaml"
    - ".xml"
    - ".ini"
    - ".conf"
    - ".config"
  source_code:
    - ".php"
    - ".py"
    - ".java"
    - ".go"
    - ".rb"
    - ".c"
    - ".cpp"
    - ".cs"

# 扫描配置
scan_config:
  max_file_size: 10485760  # 10MB
  timeout: 30
  case_sensitive: false
  multiline: true
  
# 输出配置
output_config:
  include_context: true
  context_lines: 3
  highlight_match: true
  group_by_severity: true
