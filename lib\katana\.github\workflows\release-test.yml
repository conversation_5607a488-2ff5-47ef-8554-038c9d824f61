name: 🔨 Release Test

on:
  pull_request:
    paths:
      - '**.yml'
      - '**.go'
      - '**.mod'
  workflow_dispatch:

jobs:
  release-test-mac:
    runs-on: macos-latest
    steps:
      - name: "Check out code"
        uses: actions/checkout@v4
        with: 
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x
      
      - name: release test
        uses: goreleaser/goreleaser-action@v4
        with:
          args: "release -f .goreleaser/mac.yml --clean --snapshot"
          version: latest
          workdir: .
        env: 
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"

  release-test-linux:
    runs-on: ubuntu-latest-16-cores
    steps:
      - name: "Check out code"
        uses: actions/checkout@v4
        with: 
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x

      # todo: musl compatible?
      - name: Install Dependences
        run: sudo apt update && sudo apt install gcc-aarch64-linux-gnu
      
      - name: release test
        uses: goreleaser/goreleaser-action@v4
        with:
          args: "release -f .goreleaser/linux.yml --clean --snapshot"
          version: latest
          workdir: .
        env: 
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"

  release-test-windows:
    runs-on: windows-latest-8-cores
    steps:
      - name: "Check out code"
        uses: actions/checkout@v4
        with: 
          fetch-depth: 0

      - name: Set up Go
        uses: actions/setup-go@v4
        with:
          go-version: 1.21.x
      
      - name: release test
        uses: goreleaser/goreleaser-action@v4
        with:
          args: "release -f .goreleaser/windows.yml --clean --snapshot"
          version: latest
          workdir: .
        env: 
          GITHUB_TOKEN: "${{ secrets.GITHUB_TOKEN }}"