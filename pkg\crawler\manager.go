package crawler

import (
	"context"
	"fmt"
	"sync"
	"time"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
)

// CrawlerManager 爬虫管理器
type CrawlerManager struct {
	config   *config.Config
	crawlers map[string]interfaces.Crawler
	results  chan *interfaces.CrawlerResult
	mu       sync.RWMutex
}

// NewCrawlerManager 创建爬虫管理器
func NewCrawlerManager(cfg *config.Config) *CrawlerManager {
	manager := &CrawlerManager{
		config:   cfg,
		crawlers: make(map[string]interfaces.Crawler),
		results:  make(chan *interfaces.CrawlerResult, 100),
	}

	// 初始化爬虫引擎
	if cfg.Crawler.Colly.Enable {
		colly := NewCollyEngine(cfg)
		manager.crawlers["colly"] = colly
		logger.PrintModuleStatus("Colly", true)
	}

	if cfg.Crawler.Pulse.Enable {
		pulseEngine := NewPulseEngine(cfg)
		manager.crawlers["pulse"] = pulseEngine
		logger.PrintModuleStatus("Pulse", true)
	}

	// 添加CrawlerGo引擎支持
	if cfg.Crawler.CrawlerGo.Enable {
		crawlergo := NewCrawlerGoEngine(cfg)
		manager.crawlers["crawlergo"] = crawlergo
		logger.PrintModuleStatus("CrawlerGo", true)
	}

	if cfg.Crawler.GAU.Enable {
		gau := NewGAUEngine(cfg)
		manager.crawlers["gau"] = gau
		logger.PrintModuleStatus("GAU", true)
	}

	return manager
}

// CrawlTarget 爬取目标
func (cm *CrawlerManager) CrawlTarget(ctx context.Context, target string) (*interfaces.CrawlerResult, error) {
	logger.Infof("🕷️ 开始爬取目标: %s", target)

	// 合并结果
	mergedResult := &interfaces.CrawlerResult{
		URLs:       make([]string, 0),
		Forms:      make([]interfaces.FormInfo, 0),
		JSFiles:    make([]string, 0),
		Parameters: make(map[string]string),
		Cookies:    make(map[string]string),
	}

	var wg sync.WaitGroup
	var mu sync.Mutex
	
	// 并发执行所有启用的爬虫
	for name, crawler := range cm.crawlers {
		wg.Add(1)
		go func(crawlerName string, c interfaces.Crawler) {
			defer wg.Done()
			
			logger.Infof("🔄 启动 %s 爬虫", crawlerName)
			result, err := c.Crawl(ctx, target)
			if err != nil {
				logger.Errorf("❌ %s 爬虫执行失败: %v", crawlerName, err)
				return
			}

			// 合并结果
			mu.Lock()
			mergedResult.URLs = append(mergedResult.URLs, result.URLs...)
			// 转换Form类型
			for _, form := range result.Forms {
				formInfo := interfaces.FormInfo{
					Action:  form.Action,
					Method:  form.Method,
					Fields:  form.Fields,
					Enctype: "",
				}
				mergedResult.Forms = append(mergedResult.Forms, formInfo)
			}
			mergedResult.JSFiles = append(mergedResult.JSFiles, result.JSFiles...)

			for k, v := range result.Parameters {
				mergedResult.Parameters[k] = v
			}
			// 转换Cookie类型
			for _, cookie := range result.Cookies {
				mergedResult.Cookies[cookie.Name] = cookie.Value
			}
			mu.Unlock()

			logger.Infof("✅ %s 爬虫完成，发现 %d 个URL", crawlerName, len(result.URLs))
		}(name, crawler)
	}

	// 等待所有爬虫完成
	wg.Wait()

	// 去重URL
	mergedResult.URLs = removeDuplicateStrings(mergedResult.URLs)
	mergedResult.JSFiles = removeDuplicateStrings(mergedResult.JSFiles)

	logger.Infof("🎯 爬取完成，总共发现 %d 个URL，%d 个表单，%d 个JS文件", 
		len(mergedResult.URLs), len(mergedResult.Forms), len(mergedResult.JSFiles))

	return mergedResult, nil
}

// CrawlMultipleTargets 爬取多个目标
func (cm *CrawlerManager) CrawlMultipleTargets(ctx context.Context, targets []string) ([]*interfaces.CrawlerResult, error) {
	logger.Infof("🚀 开始批量爬取，目标数量: %d", len(targets))

	results := make([]*interfaces.CrawlerResult, 0, len(targets))
	var mu sync.Mutex
	var wg sync.WaitGroup

	// 控制并发数
	semaphore := make(chan struct{}, cm.config.Crawler.Concurrency)

	for i, target := range targets {
		wg.Add(1)
		go func(index int, url string) {
			defer wg.Done()
			
			// 获取信号量
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			logger.PrintProgress(index+1, len(targets), fmt.Sprintf("爬取: %s", url))

			result, err := cm.CrawlTarget(ctx, url)
			if err != nil {
				logger.Errorf("❌ 爬取目标 %s 失败: %v", url, err)
				return
			}

			mu.Lock()
			results = append(results, result)
			mu.Unlock()
		}(i, target)
	}

	wg.Wait()

	logger.Success(fmt.Sprintf("批量爬取完成，成功爬取 %d/%d 个目标", len(results), len(targets)))
	return results, nil
}

// GetCrawlerStatus 获取爬虫状态
func (cm *CrawlerManager) GetCrawlerStatus() map[string]bool {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	status := make(map[string]bool)
	for name := range cm.crawlers {
		status[name] = true
	}

	return status
}

// Stop 停止所有爬虫
func (cm *CrawlerManager) Stop() {
	logger.Info("🛑 停止所有爬虫引擎")
	close(cm.results)
}

// removeDuplicateStrings 去重字符串切片
func removeDuplicateStrings(slice []string) []string {
	seen := make(map[string]bool)
	result := make([]string, 0)

	for _, item := range slice {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// validateURL 验证URL格式
func validateURL(url string) bool {
	// 简单的URL验证
	return len(url) > 0 && (
		len(url) > 7 && url[:7] == "http://" ||
		len(url) > 8 && url[:8] == "https://")
}

// extractDomain 提取域名
func extractDomain(url string) string {
	// 简单的域名提取
	if len(url) > 8 && url[:8] == "https://" {
		url = url[8:]
	} else if len(url) > 7 && url[:7] == "http://" {
		url = url[7:]
	}

	// 找到第一个斜杠
	if idx := findChar(url, '/'); idx != -1 {
		url = url[:idx]
	}

	return url
}

// findChar 查找字符位置
func findChar(s string, c rune) int {
	for i, char := range s {
		if char == c {
			return i
		}
	}
	return -1
}

// CrawlerStats 爬虫统计信息
type CrawlerStats struct {
	TotalURLs      int           `json:"total_urls"`
	TotalForms     int           `json:"total_forms"`
	TotalJSFiles   int           `json:"total_js_files"`
	TotalParams    int           `json:"total_params"`
	CrawlDuration  time.Duration `json:"crawl_duration"`
	ActiveCrawlers []string      `json:"active_crawlers"`
}

// GetStats 获取爬虫统计信息
func (cm *CrawlerManager) GetStats() *CrawlerStats {
	cm.mu.RLock()
	defer cm.mu.RUnlock()

	activeCrawlers := make([]string, 0, len(cm.crawlers))
	for name := range cm.crawlers {
		activeCrawlers = append(activeCrawlers, name)
	}

	return &CrawlerStats{
		ActiveCrawlers: activeCrawlers,
	}
}
