# Pulsex 测试靶场

这是一个专门为测试Pulsex安全扫描器而设计的Web漏洞靶场，包含多种常见的Web安全漏洞。

## 🎯 包含的漏洞类型

### 1. XSS (跨站脚本攻击)
- **反射型XSS**: `/xss/reflected?q=<script>alert('XSS')</script>`
- **存储型XSS**: `/xss/stored` - 提交包含脚本的留言
- **DOM型XSS**: `/xss/dom#<img src=x onerror=alert('DOM XSS')>`

### 2. SQL注入
- **登录SQL注入**: `/sqli/login` - 用户名: `admin' OR '1'='1`
- **搜索SQL注入**: `/sqli/search?q=' UNION SELECT password, role FROM users--`
- **查询SQL注入**: `/sqli/user?id=1 UNION SELECT 1,username,password,role FROM users`

### 3. 敏感信息泄露
- **配置文件**: `/sensitive/config` - 包含数据库密码、API密钥等
- **备份文件**: `/sensitive/backup` - 数据库备份和密码信息
- **调试信息**: `/sensitive/debug` - JSON格式的调试信息

### 4. 未授权访问
- **管理员面板**: `/admin` - 无需认证即可访问
- **用户管理**: `/admin/users` - 显示所有用户信息包括密码
- **用户API**: `/api/users` - 返回用户列表
- **管理员配置API**: `/api/admin/config` - 返回敏感配置信息

### 5. 目录遍历
- **文件列表**: `/files` - 显示可下载的文件列表
- **文件下载**: `/download?file=../etc/passwd` - 目录遍历漏洞

### 6. JavaScript敏感信息
- **应用JS**: `/js/app.js` - 包含API密钥和敏感函数
- **配置JS**: `/js/config.js` - 暴露数据库密码和API密钥
- **API接口JS**: `/js/api.js` - 包含API端点和认证信息

### 7. 隐藏资源
- **备份目录**: `/backup/` - 包含备份文件的目录
- **环境配置**: `/.env` - 环境变量文件
- **Robots文件**: `/robots.txt` - 泄露敏感目录信息
- **站点地图**: `/sitemap.xml` - 包含隐藏页面链接

## 🚀 快速开始

### 安装依赖
```bash
cd testlab
go mod tidy
```

### 启动靶场
```bash
# 默认端口8888
go run main.go

# 自定义端口
go run main.go 9999
```

### 访问靶场
打开浏览器访问: http://localhost:8888

## 🔧 测试Pulsex扫描器

### 基本扫描
```bash
# 扫描整个靶场
pulsex -u http://localhost:8888

# 仅XSS扫描
pulsex -u http://localhost:8888 -xss

# 仅SQL注入扫描
pulsex -u http://localhost:8888 -sqli

# 敏感信息扫描
pulsex -u http://localhost:8888 -sensitive

# 未授权访问扫描
pulsex -u http://localhost:8888 -unauth

# JavaScript分析
pulsex -u http://localhost:8888 -js
```

### 被动扫描测试
```bash
# 启动Pulsex代理
pulsex -passive -proxy-port 8080

# 配置浏览器代理为127.0.0.1:8080
# 然后正常浏览靶场网站
```

### 爬虫测试
```bash
# 测试爬虫功能
pulsex -u http://localhost:8888 -crawl-only

# 使用不同爬虫
pulsex -u http://localhost:8888 -gau -colly -katana
```

## 📊 预期检测结果

运行Pulsex扫描器后，应该能检测到以下漏洞：

### XSS漏洞
- 反射型XSS: `/xss/reflected`
- 存储型XSS: `/xss/stored`
- DOM型XSS: `/xss/dom`

### SQL注入漏洞
- 登录页面SQL注入: `/sqli/login`
- 搜索功能SQL注入: `/sqli/search`
- 用户查询SQL注入: `/sqli/user`

### 敏感信息
- 数据库密码: `super_secret_password123`
- API密钥: `sk_live_1234567890abcdefghijklmnopqrstuvwxyz`
- JWT密钥: `jwt_super_secret_key_2023`
- 管理员邮箱: `<EMAIL>`
- 服务器IP: `*************`

### 未授权访问
- 管理员面板: `/admin`
- 用户管理: `/admin/users`
- API端点: `/api/users`, `/api/admin/config`

### 目录遍历
- 系统文件: `/download?file=../etc/passwd`
- 配置文件: `/download?file=config.txt`

## 🛡️ 安全提醒

⚠️ **重要提醒**: 
- 本靶场仅用于安全测试和教育目的
- 请勿在生产环境中部署
- 请勿用于非法用途
- 建议在隔离的测试环境中运行

## 📝 漏洞详情

### 数据库结构
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username TEXT NOT NULL,
    email TEXT NOT NULL,
    password TEXT NOT NULL,
    role TEXT DEFAULT 'user'
);
```

### 测试用户
- admin / admin123 (管理员)
- user1 / password123 (普通用户)
- test / test123 (测试用户)
- guest / guest (访客)

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个测试靶场！

## 📄 许可证

本项目采用MIT许可证。

---

**Pulsex测试靶场** - 让安全测试更简单！
