{"target": "http://192.168.11.1:8888/", "scan_time": "2025-07-30T14:09:42.1932751+08:00", "duration": "141.6868ms", "version": "1.0.0", "summary": {"total_urls": 10, "total_js_files": 0, "total_parameters": 0, "total_forms": 0, "total_cookies": 0, "total_subdomains": 0, "gau_urls": 0, "colly_urls": 0, "pulse_urls": 10, "crawlergo_urls": 0}, "crawl_results": {"urls": [{"url": "http://192.168.11.1:8888/", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/bootstrap4.min.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/base.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/codemirror.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/signin.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/linearicons.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/mdn-like.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/captcha", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/captcha?d=", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "LOW"}, {"url": "http://192.168.11.1:8888/user/login;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "Pulse", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}, {"url": "http://192.168.11.1:8888/", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/bootstrap4.min.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/base.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/codemirror.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/signin.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/linearicons.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/css/mdn-like.css;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/captcha", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "INFO"}, {"url": "http://192.168.11.1:8888/captcha?d=", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "LOW"}, {"url": "http://192.168.11.1:8888/user/login;jsessionid=5F9584AEECEE1EA57744EEB3C011FB59", "source": "<PERSON>rged", "status_code": 200, "title": "", "length": 0, "risk_level": "HIGH"}], "js_files": [], "parameters": [], "forms": [], "cookies": [], "subdomains": [], "technologies": []}, "xss_results": [], "vulnerabilities": {"critical": 0, "high": 0, "medium": 0, "low": 0, "info": 0, "total": 0}, "risk_assessment": {"overall_risk": "MEDIUM", "risk_score": 50, "risk_factors": [], "recommendations": ["定期进行安全扫描", "实施输入验证和输出编码", "使用HTTPS加密传输"], "categories": {"低风险URL": 2, "高风险URL": 2}}}