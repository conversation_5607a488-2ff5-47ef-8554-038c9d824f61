package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gorilla/mux"
	_ "github.com/mattn/go-sqlite3"
)

// User 用户结构
type User struct {
	ID       int    `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
	Role     string `json:"role"`
}

// VulnLab 漏洞靶场
type VulnLab struct {
	db     *sql.DB
	router *mux.Router
}

// NewVulnLab 创建新的漏洞靶场
func NewVulnLab() *VulnLab {
	lab := &VulnLab{}
	lab.initDB()
	lab.setupRoutes()
	return lab
}

// initDB 初始化数据库
func (lab *VulnLab) initDB() {
	var err error
	lab.db, err = sql.Open("sqlite3", "./testlab.db")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 创建用户表
	createTable := `
	CREATE TABLE IF NOT EXISTS users (
		id INTEGER PRIMARY KEY AUTOINCREMENT,
		username TEXT NOT NULL,
		email TEXT NOT NULL,
		password TEXT NOT NULL,
		role TEXT DEFAULT 'user'
	);`

	_, err = lab.db.Exec(createTable)
	if err != nil {
		log.Fatal("创建表失败:", err)
	}

	// 插入测试数据
	lab.insertTestData()
}

// insertTestData 插入测试数据
func (lab *VulnLab) insertTestData() {
	users := []User{
		{Username: "admin", Email: "<EMAIL>", Password: "admin123", Role: "admin"},
		{Username: "user1", Email: "<EMAIL>", Password: "password123", Role: "user"},
		{Username: "test", Email: "<EMAIL>", Password: "test123", Role: "user"},
		{Username: "guest", Email: "<EMAIL>", Password: "guest", Role: "guest"},
	}

	for _, user := range users {
		lab.db.Exec("INSERT OR IGNORE INTO users (username, email, password, role) VALUES (?, ?, ?, ?)",
			user.Username, user.Email, user.Password, user.Role)
	}
}

// setupRoutes 设置路由
func (lab *VulnLab) setupRoutes() {
	lab.router = mux.NewRouter()

	// 静态文件服务
	lab.router.PathPrefix("/static/").Handler(http.StripPrefix("/static/", http.FileServer(http.Dir("./static/"))))

	// 主页
	lab.router.HandleFunc("/", lab.homeHandler).Methods("GET")

	// XSS漏洞测试
	lab.router.HandleFunc("/xss/reflected", lab.reflectedXSSHandler).Methods("GET")
	lab.router.HandleFunc("/xss/stored", lab.storedXSSHandler).Methods("GET", "POST")
	lab.router.HandleFunc("/xss/dom", lab.domXSSHandler).Methods("GET")

	// SQL注入漏洞测试
	lab.router.HandleFunc("/sqli/login", lab.sqlInjectionLoginHandler).Methods("GET", "POST")
	lab.router.HandleFunc("/sqli/search", lab.sqlInjectionSearchHandler).Methods("GET")
	lab.router.HandleFunc("/sqli/user", lab.sqlInjectionUserHandler).Methods("GET")

	// 敏感信息泄露
	lab.router.HandleFunc("/sensitive/config", lab.sensitiveConfigHandler).Methods("GET")
	lab.router.HandleFunc("/sensitive/backup", lab.sensitiveBackupHandler).Methods("GET")
	lab.router.HandleFunc("/sensitive/debug", lab.sensitiveDebugHandler).Methods("GET")

	// 未授权访问
	lab.router.HandleFunc("/admin", lab.adminPanelHandler).Methods("GET")
	lab.router.HandleFunc("/admin/users", lab.adminUsersHandler).Methods("GET")
	lab.router.HandleFunc("/api/users", lab.apiUsersHandler).Methods("GET")
	lab.router.HandleFunc("/api/admin/config", lab.apiAdminConfigHandler).Methods("GET")

	// 目录遍历
	lab.router.HandleFunc("/files", lab.fileListHandler).Methods("GET")
	lab.router.HandleFunc("/download", lab.fileDownloadHandler).Methods("GET")

	// JavaScript相关
	lab.router.HandleFunc("/js/app.js", lab.jsAppHandler).Methods("GET")
	lab.router.HandleFunc("/js/config.js", lab.jsConfigHandler).Methods("GET")
	lab.router.HandleFunc("/js/api.js", lab.jsAPIHandler).Methods("GET")

	// 隐藏目录和文件
	lab.router.HandleFunc("/backup/", lab.backupDirHandler).Methods("GET")
	lab.router.HandleFunc("/.env", lab.envFileHandler).Methods("GET")
	lab.router.HandleFunc("/robots.txt", lab.robotsHandler).Methods("GET")
	lab.router.HandleFunc("/sitemap.xml", lab.sitemapHandler).Methods("GET")

	// API端点
	lab.router.HandleFunc("/api/v1/users", lab.apiV1UsersHandler).Methods("GET", "POST")
	lab.router.HandleFunc("/api/v2/users/{id}", lab.apiV2UserHandler).Methods("GET", "PUT", "DELETE")
}

// homeHandler 主页处理器
func (lab *VulnLab) homeHandler(w http.ResponseWriter, r *http.Request) {
	// 设置正确的Content-Type和字符编码
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	html := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pulsex 测试靶场</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 40px;
            background-color: #f5f5f5;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .vuln-link {
            display: block;
            margin: 8px 0;
            color: #007bff;
            text-decoration: none;
            padding: 5px 10px;
            border-radius: 4px;
            transition: background-color 0.3s;
        }
        .vuln-link:hover {
            text-decoration: underline;
            background-color: #e3f2fd;
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 10px;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #007bff;
            padding-bottom: 5px;
        }
        .intro {
            text-align: center;
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 Pulsex 安全扫描器测试靶场</h1>
        <p class="intro">这是一个专门为测试Pulsex扫描器而设计的漏洞靶场，包含各种常见的Web安全漏洞。</p>

        <div class="warning">
            <strong>⚠️ 安全提醒:</strong> 本靶场仅用于安全测试和教育目的，请勿在生产环境中部署！
        </div>
    
    <div class="section">
        <h2>🔥 XSS 跨站脚本攻击</h2>
        <a href="/xss/reflected?q=test" class="vuln-link">反射型XSS测试</a>
        <a href="/xss/stored" class="vuln-link">存储型XSS测试</a>
        <a href="/xss/dom" class="vuln-link">DOM型XSS测试</a>
    </div>
    
    <div class="section">
        <h2>💉 SQL注入攻击</h2>
        <a href="/sqli/login" class="vuln-link">登录SQL注入</a>
        <a href="/sqli/search?q=admin" class="vuln-link">搜索SQL注入</a>
        <a href="/sqli/user?id=1" class="vuln-link">用户查询SQL注入</a>
    </div>
    
    <div class="section">
        <h2>🔐 敏感信息泄露</h2>
        <a href="/sensitive/config" class="vuln-link">配置文件泄露</a>
        <a href="/sensitive/backup" class="vuln-link">备份文件泄露</a>
        <a href="/sensitive/debug" class="vuln-link">调试信息泄露</a>
    </div>
    
    <div class="section">
        <h2>🚫 未授权访问</h2>
        <a href="/admin" class="vuln-link">管理员面板</a>
        <a href="/admin/users" class="vuln-link">用户管理</a>
        <a href="/api/users" class="vuln-link">用户API</a>
        <a href="/api/admin/config" class="vuln-link">管理员配置API</a>
    </div>
    
    <div class="section">
        <h2>📁 目录遍历</h2>
        <a href="/files" class="vuln-link">文件列表</a>
        <a href="/download?file=config.txt" class="vuln-link">文件下载</a>
    </div>
    
    <div class="section">
        <h2>📜 JavaScript分析</h2>
        <a href="/js/app.js" class="vuln-link">应用程序JS</a>
        <a href="/js/config.js" class="vuln-link">配置JS</a>
        <a href="/js/api.js" class="vuln-link">API接口JS</a>
    </div>
    
    <div class="section">
        <h2>🔍 隐藏资源</h2>
        <a href="/backup/" class="vuln-link">备份目录</a>
        <a href="/.env" class="vuln-link">环境配置文件</a>
        <a href="/robots.txt" class="vuln-link">Robots文件</a>
        <a href="/sitemap.xml" class="vuln-link">站点地图</a>
    </div>
    
        <script>
            // 一些客户端代码，包含敏感信息
            var apiKey = "sk_test_1234567890abcdef";
            var dbPassword = "super_secret_password";
            var adminToken = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYWRtaW4ifQ.test";

            console.log("Debug: API Key =", apiKey);
            console.log("Debug: DB Password =", dbPassword);
        </script>
    </div>
</body>
</html>`

	fmt.Fprint(w, html)
}

// reflectedXSSHandler 反射型XSS处理器
func (lab *VulnLab) reflectedXSSHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	query := r.URL.Query().Get("q")
	if query == "" {
		query = "请输入搜索内容"
	}

	html := fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>搜索结果 - Pulsex测试靶场</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .search-form { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 5px; }
        .search-input { width: 300px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; }
        .search-btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        .result { margin: 20px 0; padding: 15px; background: #e8f5e8; border-radius: 5px; }
        .back-link { color: #007bff; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 搜索结果</h1>
        <div class="result">
            <p><strong>您搜索的内容是:</strong> %s</p>
        </div>
        <div class="search-form">
            <form method="GET">
                <input type="text" name="q" value="%s" placeholder="输入搜索内容" class="search-input">
                <button type="submit" class="search-btn">搜索</button>
            </form>
        </div>
        <p><a href="/" class="back-link">← 返回首页</a></p>
        <div style="margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px;">
            <strong>💡 XSS测试提示:</strong> 尝试输入 <code>&lt;script&gt;alert('XSS')&lt;/script&gt;</code>
        </div>
    </div>
</body>
</html>`, query, query) // 直接输出用户输入，存在XSS漏洞

	fmt.Fprint(w, html)
}

// storedXSSHandler 存储型XSS处理器
func (lab *VulnLab) storedXSSHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	if r.Method == "POST" {
		comment := r.FormValue("comment")
		if comment != "" {
			// 存储评论到文件（简化实现）
			file, err := os.OpenFile("comments.txt", os.O_APPEND|os.O_CREATE|os.O_WRONLY, 0644)
			if err == nil {
				file.WriteString(comment + "\n")
				file.Close()
			}
		}
	}

	// 读取所有评论
	comments := ""
	if data, err := os.ReadFile("comments.txt"); err == nil {
		for _, line := range strings.Split(string(data), "\n") {
			if line != "" {
				comments += fmt.Sprintf(`<div class="comment">%s</div>`, line) // 直接输出，存在XSS漏洞
			}
		}
	}

	if comments == "" {
		comments = `<div class="no-comments">暂无留言，快来抢沙发吧！</div>`
	}

	html := fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>留言板 - Pulsex测试靶场</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .comment-form { margin: 20px 0; padding: 20px; background: #f8f9fa; border-radius: 5px; }
        .comment-textarea { width: 100%%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; resize: vertical; font-family: inherit; }
        .submit-btn { padding: 10px 20px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer; margin-top: 10px; }
        .submit-btn:hover { background: #218838; }
        .comments-section { margin: 30px 0; }
        .comment { margin: 10px 0; padding: 15px; background: #e8f5e8; border-left: 4px solid #28a745; border-radius: 4px; }
        .no-comments { text-align: center; color: #666; font-style: italic; padding: 20px; }
        .back-link { color: #007bff; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .tip { margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>💬 留言板</h1>
        <div class="comment-form">
            <form method="POST">
                <textarea name="comment" placeholder="请输入留言内容..." rows="4" class="comment-textarea" required></textarea><br>
                <button type="submit" class="submit-btn">📝 提交留言</button>
            </form>
        </div>
        <div class="comments-section">
            <h2>📋 所有留言:</h2>
            %s
        </div>
        <p><a href="/" class="back-link">← 返回首页</a></p>
        <div class="tip">
            <strong>💡 XSS测试提示:</strong> 尝试提交包含脚本的留言，如 <code>&lt;script&gt;alert('Stored XSS')&lt;/script&gt;</code>
        </div>
    </div>
</body>
</html>`, comments)

	fmt.Fprint(w, html)
}

// domXSSHandler DOM型XSS处理器
func (lab *VulnLab) domXSSHandler(w http.ResponseWriter, r *http.Request) {
	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	html := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM XSS测试 - Pulsex测试靶场</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        #content { margin: 20px 0; padding: 20px; background: #e8f5e8; border-radius: 5px; min-height: 50px; border: 2px dashed #28a745; }
        .example { margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .test-link { color: #dc3545; font-weight: bold; text-decoration: none; }
        .test-link:hover { text-decoration: underline; }
        .back-link { color: #007bff; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .tip { margin-top: 20px; padding: 10px; background: #fff3cd; border-radius: 5px; font-size: 14px; }
        .placeholder { color: #666; font-style: italic; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎭 DOM XSS测试</h1>
        <p>这个页面演示了DOM型XSS漏洞，JavaScript会直接将URL片段内容插入到页面中。</p>

        <div id="content" class="placeholder">请在URL后添加#内容来测试DOM XSS</div>

        <div class="example">
            <h3>🧪 测试示例:</h3>
            <p>点击下面的链接测试DOM XSS:</p>
            <p><a href="/xss/dom#<img src=x onerror=alert('DOM XSS')>" class="test-link">测试DOM XSS攻击</a></p>
            <p><a href="/xss/dom#<h2 style='color:red'>DOM内容注入</h2>" class="test-link">测试DOM内容注入</a></p>
        </div>

        <p><a href="/" class="back-link">← 返回首页</a></p>

        <div class="tip">
            <strong>💡 DOM XSS测试提示:</strong>
            <br>• 在URL后添加 <code>#&lt;script&gt;alert('DOM XSS')&lt;/script&gt;</code>
            <br>• 或者添加 <code>#&lt;img src=x onerror=alert('XSS')&gt;</code>
        </div>
    </div>

    <script>
        // 从URL片段获取内容并直接插入DOM (存在XSS漏洞)
        var hash = window.location.hash.substring(1);
        if (hash) {
            document.getElementById('content').innerHTML = decodeURIComponent(hash);
            document.getElementById('content').className = ''; // 移除placeholder样式
        }
    </script>
</body>
</html>`

	fmt.Fprint(w, html)
}

// sqlInjectionLoginHandler SQL注入登录处理器
func (lab *VulnLab) sqlInjectionLoginHandler(w http.ResponseWriter, r *http.Request) {
	message := ""

	if r.Method == "POST" {
		username := r.FormValue("username")
		password := r.FormValue("password")

		// 存在SQL注入漏洞的查询
		query := fmt.Sprintf("SELECT * FROM users WHERE username='%s' AND password='%s'", username, password)

		rows, err := lab.db.Query(query)
		if err != nil {
			message = "数据库错误: " + err.Error()
		} else {
			defer rows.Close()
			if rows.Next() {
				var user User
				rows.Scan(&user.ID, &user.Username, &user.Email, &user.Password, &user.Role)
				message = fmt.Sprintf("登录成功! 欢迎 %s (%s)", user.Username, user.Role)
			} else {
				message = "用户名或密码错误"
			}
		}
	}

	w.Header().Set("Content-Type", "text/html; charset=utf-8")

	html := fmt.Sprintf(`<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - Pulsex测试靶场</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 40px; background: #f5f5f5; }
        .container { max-width: 500px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .login-form { margin: 20px 0; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; color: #333; }
        .form-group input { width: 100%%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; }
        .login-btn { width: 100%%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; font-size: 16px; margin-top: 10px; }
        .login-btn:hover { background: #0056b3; }
        .message { margin: 15px 0; padding: 10px; border-radius: 4px; }
        .message.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .back-link { color: #007bff; text-decoration: none; }
        .back-link:hover { text-decoration: underline; }
        .tip { margin-top: 20px; padding: 15px; background: #fff3cd; border-radius: 5px; font-size: 14px; }
        .test-accounts { margin: 15px 0; padding: 15px; background: #f8f9fa; border-radius: 5px; }
        .test-accounts h4 { margin-top: 0; color: #495057; }
        .test-accounts ul { margin: 5px 0; padding-left: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 用户登录</h1>
        <div class="login-form">
            <form method="POST">
                <div class="form-group">
                    <label for="username">用户名:</label>
                    <input type="text" id="username" name="username" required placeholder="请输入用户名">
                </div>
                <div class="form-group">
                    <label for="password">密码:</label>
                    <input type="password" id="password" name="password" required placeholder="请输入密码">
                </div>
                <button type="submit" class="login-btn">🚀 登录</button>
            </form>
        </div>

        %s

        <div class="test-accounts">
            <h4>📋 测试账户:</h4>
            <ul>
                <li>admin / admin123 (管理员)</li>
                <li>user1 / password123 (普通用户)</li>
                <li>test / test123 (测试用户)</li>
            </ul>
        </div>

        <p><a href="/" class="back-link">← 返回首页</a></p>

        <div class="tip">
            <strong>💡 SQL注入测试提示:</strong>
            <br>• 用户名输入: <code>admin' OR '1'='1</code>
            <br>• 或者输入: <code>' OR 1=1 --</code>
            <br>• 密码可以随意输入
        </div>
    </div>
</body>
</html>`, func() string {
		if message != "" {
			if strings.Contains(message, "成功") {
				return fmt.Sprintf(`<div class="message success">✅ %s</div>`, message)
			} else {
				return fmt.Sprintf(`<div class="message error">❌ %s</div>`, message)
			}
		}
		return ""
	}())

	fmt.Fprint(w, html)
}

// sqlInjectionSearchHandler SQL注入搜索处理器
func (lab *VulnLab) sqlInjectionSearchHandler(w http.ResponseWriter, r *http.Request) {
	query := r.URL.Query().Get("q")
	results := ""

	if query != "" {
		// 存在SQL注入漏洞的搜索查询
		sqlQuery := fmt.Sprintf("SELECT username, email FROM users WHERE username LIKE '%%%s%%'", query)

		rows, err := lab.db.Query(sqlQuery)
		if err != nil {
			results = "搜索错误: " + err.Error()
		} else {
			defer rows.Close()
			results = "<h2>搜索结果:</h2>"
			for rows.Next() {
				var username, email string
				rows.Scan(&username, &email)
				results += fmt.Sprintf("<div>用户: %s, 邮箱: %s</div>", username, email)
			}
		}
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head><title>用户搜索</title></head>
<body>
    <h1>用户搜索</h1>
    <form method="GET">
        <input type="text" name="q" value="%s" placeholder="输入用户名搜索">
        <button type="submit">搜索</button>
    </form>
    <div>%s</div>
    <p>提示: 尝试输入 ' UNION SELECT password, role FROM users-- 进行联合查询</p>
    <a href="/">返回首页</a>
</body>
</html>`, query, results)

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// sqlInjectionUserHandler SQL注入用户查询处理器
func (lab *VulnLab) sqlInjectionUserHandler(w http.ResponseWriter, r *http.Request) {
	id := r.URL.Query().Get("id")
	userInfo := ""

	if id != "" {
		// 存在SQL注入漏洞的用户查询
		query := fmt.Sprintf("SELECT id, username, email, role FROM users WHERE id = %s", id)

		rows, err := lab.db.Query(query)
		if err != nil {
			userInfo = "查询错误: " + err.Error()
		} else {
			defer rows.Close()
			if rows.Next() {
				var user User
				rows.Scan(&user.ID, &user.Username, &user.Email, &user.Role)
				userInfo = fmt.Sprintf(`
					<h2>用户信息:</h2>
					<p>ID: %d</p>
					<p>用户名: %s</p>
					<p>邮箱: %s</p>
					<p>角色: %s</p>
				`, user.ID, user.Username, user.Email, user.Role)
			} else {
				userInfo = "用户不存在"
			}
		}
	}

	html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head><title>用户信息查询</title></head>
<body>
    <h1>用户信息查询</h1>
    <form method="GET">
        <label>用户ID:</label>
        <input type="text" name="id" value="%s" placeholder="输入用户ID">
        <button type="submit">查询</button>
    </form>
    <div>%s</div>
    <p>提示: 尝试输入 1 UNION SELECT 1,username,password,role FROM users 进行联合查询</p>
    <a href="/">返回首页</a>
</body>
</html>`, id, userInfo)

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// sensitiveConfigHandler 敏感配置信息处理器
func (lab *VulnLab) sensitiveConfigHandler(w http.ResponseWriter, r *http.Request) {
	config := `# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=super_secret_password123
DB_NAME=testlab

# API密钥
API_KEY=sk_live_1234567890abcdefghijklmnopqrstuvwxyz
SECRET_KEY=your_secret_key_here_12345
JWT_SECRET=jwt_super_secret_key_2023

# 第三方服务
STRIPE_SECRET_KEY=sk_live_51234567890abcdef
AWS_ACCESS_KEY_ID=AKIA1234567890ABCDEF
AWS_SECRET_ACCESS_KEY=abcdefghijklmnopqrstuvwxyz1234567890ABCDEF
GOOGLE_API_KEY=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI

# 邮件配置
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=email_password_123

# 管理员信息
ADMIN_EMAIL=<EMAIL>
ADMIN_PHONE=13800138000
ADMIN_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYWRtaW4ifQ.test

# 调试模式
DEBUG=true
LOG_LEVEL=debug`

	w.Header().Set("Content-Type", "text/plain")
	fmt.Fprint(w, config)
}

// sensitiveBackupHandler 敏感备份文件处理器
func (lab *VulnLab) sensitiveBackupHandler(w http.ResponseWriter, r *http.Request) {
	backup := `-- 数据库备份文件
-- 生成时间: 2024-01-01 12:00:00
-- 数据库: testlab

INSERT INTO users VALUES (1, 'admin', '<EMAIL>', 'admin123', 'admin');
INSERT INTO users VALUES (2, 'user1', '<EMAIL>', 'password123', 'user');
INSERT INTO users VALUES (3, 'test', '<EMAIL>', 'test123', 'user');
INSERT INTO users VALUES (4, 'guest', '<EMAIL>', 'guest', 'guest');

-- 管理员密码: admin123
-- 数据库密码: super_secret_password123
-- API密钥: sk_live_1234567890abcdefghijklmnopqrstuvwxyz

-- 服务器信息
-- IP: *************
-- SSH密码: ssh_password_123
-- Root密码: root_password_456`

	w.Header().Set("Content-Type", "text/plain")
	fmt.Fprint(w, backup)
}

// sensitiveDebugHandler 敏感调试信息处理器
func (lab *VulnLab) sensitiveDebugHandler(w http.ResponseWriter, r *http.Request) {
	debug := map[string]interface{}{
		"debug_mode":     true,
		"database_host":  "localhost:3306",
		"database_user":  "root",
		"database_pass":  "super_secret_password123",
		"api_key":        "sk_live_1234567890abcdefghijklmnopqrstuvwxyz",
		"jwt_secret":     "jwt_super_secret_key_2023",
		"admin_email":    "<EMAIL>",
		"admin_phone":    "13800138000",
		"server_ip":      "*************",
		"internal_ip":    "**********",
		"version":        "1.0.0-debug",
		"build_time":     "2024-01-01 12:00:00",
		"environment":    "development",
		"log_level":      "debug",
		"stack_trace": []string{
			"main.go:123",
			"handler.go:456",
			"database.go:789",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(debug)
}

// adminPanelHandler 管理员面板处理器（未授权访问）
func (lab *VulnLab) adminPanelHandler(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html>
<head><title>管理员面板</title></head>
<body>
    <h1>🔐 管理员面板</h1>
    <p>欢迎来到管理员面板！这里包含敏感的管理功能。</p>

    <div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
        <h2>系统信息</h2>
        <p>服务器IP: *************</p>
        <p>数据库: MySQL 8.0</p>
        <p>当前用户: admin</p>
        <p>权限级别: 超级管理员</p>
    </div>

    <div style="border: 1px solid #ccc; padding: 20px; margin: 20px 0;">
        <h2>快速操作</h2>
        <a href="/admin/users" style="display: block; margin: 5px 0;">用户管理</a>
        <a href="/api/admin/config" style="display: block; margin: 5px 0;">系统配置</a>
        <a href="/sensitive/debug" style="display: block; margin: 5px 0;">调试信息</a>
    </div>

    <a href="/">返回首页</a>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// adminUsersHandler 管理员用户管理处理器
func (lab *VulnLab) adminUsersHandler(w http.ResponseWriter, r *http.Request) {
	rows, err := lab.db.Query("SELECT id, username, email, password, role FROM users")
	if err != nil {
		http.Error(w, "数据库错误", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	users := []User{}
	for rows.Next() {
		var user User
		rows.Scan(&user.ID, &user.Username, &user.Email, &user.Password, &user.Role)
		users = append(users, user)
	}

	html := `
<!DOCTYPE html>
<html>
<head><title>用户管理</title></head>
<body>
    <h1>👥 用户管理</h1>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>ID</th>
            <th>用户名</th>
            <th>邮箱</th>
            <th>密码</th>
            <th>角色</th>
        </tr>`

	for _, user := range users {
		html += fmt.Sprintf(`
        <tr>
            <td>%d</td>
            <td>%s</td>
            <td>%s</td>
            <td>%s</td>
            <td>%s</td>
        </tr>`, user.ID, user.Username, user.Email, user.Password, user.Role)
	}

	html += `
    </table>
    <a href="/admin">返回管理面板</a> | <a href="/">返回首页</a>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// apiUsersHandler API用户列表处理器
func (lab *VulnLab) apiUsersHandler(w http.ResponseWriter, r *http.Request) {
	rows, err := lab.db.Query("SELECT id, username, email, role FROM users")
	if err != nil {
		http.Error(w, "数据库错误", http.StatusInternalServerError)
		return
	}
	defer rows.Close()

	users := []User{}
	for rows.Next() {
		var user User
		rows.Scan(&user.ID, &user.Username, &user.Email, &user.Role)
		users = append(users, user)
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"status": "success",
		"data":   users,
		"count":  len(users),
	})
}

// apiAdminConfigHandler API管理员配置处理器
func (lab *VulnLab) apiAdminConfigHandler(w http.ResponseWriter, r *http.Request) {
	config := map[string]interface{}{
		"database": map[string]string{
			"host":     "localhost",
			"port":     "3306",
			"username": "root",
			"password": "super_secret_password123",
			"database": "testlab",
		},
		"api_keys": map[string]string{
			"stripe":    "sk_live_51234567890abcdef",
			"aws":       "AKIA1234567890ABCDEF",
			"google":    "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI",
			"jwt_secret": "jwt_super_secret_key_2023",
		},
		"admin": map[string]string{
			"email":    "<EMAIL>",
			"phone":    "13800138000",
			"password": "admin123",
		},
		"server": map[string]interface{}{
			"debug":       true,
			"environment": "development",
			"version":     "1.0.0",
			"ip":          "*************",
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(config)
}

// fileListHandler 文件列表处理器
func (lab *VulnLab) fileListHandler(w http.ResponseWriter, r *http.Request) {
	files := []map[string]interface{}{
		{"name": "config.txt", "size": "1.2KB", "type": "config"},
		{"name": "backup.sql", "size": "15.6KB", "type": "database"},
		{"name": "users.csv", "size": "3.4KB", "type": "data"},
		{"name": "passwords.txt", "size": "2.1KB", "type": "sensitive"},
		{"name": "api_keys.json", "size": "0.8KB", "type": "sensitive"},
		{"name": "server.log", "size": "45.2KB", "type": "log"},
		{"name": "../etc/passwd", "size": "2.3KB", "type": "system"},
		{"name": "../etc/shadow", "size": "1.1KB", "type": "system"},
	}

	html := `
<!DOCTYPE html>
<html>
<head><title>文件列表</title></head>
<body>
    <h1>📁 文件列表</h1>
    <table border="1" style="border-collapse: collapse; width: 100%;">
        <tr>
            <th>文件名</th>
            <th>大小</th>
            <th>类型</th>
            <th>操作</th>
        </tr>`

	for _, file := range files {
		html += fmt.Sprintf(`
        <tr>
            <td>%s</td>
            <td>%s</td>
            <td>%s</td>
            <td><a href="/download?file=%s">下载</a></td>
        </tr>`, file["name"], file["size"], file["type"], file["name"])
	}

	html += `
    </table>
    <a href="/">返回首页</a>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// fileDownloadHandler 文件下载处理器（目录遍历漏洞）
func (lab *VulnLab) fileDownloadHandler(w http.ResponseWriter, r *http.Request) {
	filename := r.URL.Query().Get("file")
	if filename == "" {
		http.Error(w, "请指定文件名", http.StatusBadRequest)
		return
	}

	// 存在目录遍历漏洞 - 直接使用用户输入的文件名
	var content string
	switch filename {
	case "config.txt":
		content = `# 应用配置文件
app_name=TestLab
debug=true
database_url=mysql://root:super_secret_password123@localhost:3306/testlab
api_key=sk_live_1234567890abcdefghijklmnopqrstuvwxyz
jwt_secret=jwt_super_secret_key_2023
admin_email=<EMAIL>
admin_password=admin123`

	case "passwords.txt":
		content = `# 用户密码列表
admin:admin123
user1:password123
test:test123
guest:guest
root:root_password_456
ssh_user:ssh_password_123`

	case "api_keys.json":
		content = `{
  "stripe_secret": "sk_live_51234567890abcdef",
  "aws_access_key": "AKIA1234567890ABCDEF",
  "aws_secret_key": "abcdefghijklmnopqrstuvwxyz1234567890ABCDEF",
  "google_api_key": "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI",
  "jwt_secret": "jwt_super_secret_key_2023"
}`

	case "../etc/passwd":
		content = `root:x:0:0:root:/root:/bin/bash
daemon:x:1:1:daemon:/usr/sbin:/usr/sbin/nologin
bin:x:2:2:bin:/bin:/usr/sbin/nologin
sys:x:3:3:sys:/dev:/usr/sbin/nologin
www-data:x:33:33:www-data:/var/www:/usr/sbin/nologin
admin:x:1000:1000:admin:/home/<USER>/bin/bash`

	case "../etc/shadow":
		content = `root:$6$randomsalt$hashedpassword:18000:0:99999:7:::
admin:$6$randomsalt$hashedpassword:18000:0:99999:7:::`

	default:
		content = fmt.Sprintf("文件内容: %s\n这是一个测试文件，包含敏感信息。", filename)
	}

	w.Header().Set("Content-Type", "text/plain")
	w.Header().Set("Content-Disposition", fmt.Sprintf("attachment; filename=%s", filepath.Base(filename)))
	fmt.Fprint(w, content)
}

// jsAppHandler JavaScript应用文件处理器
func (lab *VulnLab) jsAppHandler(w http.ResponseWriter, r *http.Request) {
	js := `// 主应用程序 JavaScript 文件
// 包含敏感信息和API端点

// 配置信息
const CONFIG = {
    apiKey: "sk_live_1234567890abcdefghijklmnopqrstuvwxyz",
    apiSecret: "your_secret_key_here_12345",
    databaseUrl: "mysql://root:super_secret_password123@localhost:3306/testlab",
    jwtSecret: "jwt_super_secret_key_2023",
    adminEmail: "<EMAIL>",
    adminPassword: "admin123", // TODO: 移除硬编码密码
    debugMode: true
};

// API端点
const API_ENDPOINTS = {
    users: "/api/users",
    admin: "/api/admin/config",
    login: "/api/auth/login",
    upload: "/api/upload",
    delete: "/api/delete",
    backup: "/api/backup/download"
};

// 敏感函数
function authenticateAdmin(username, password) {
    // 硬编码的管理员凭据
    if (username === "admin" && password === "admin123") {
        return generateToken({role: "admin", user: username});
    }
    return null;
}

function generateToken(payload) {
    // 简化的JWT生成（不安全）
    const header = btoa(JSON.stringify({alg: "none", typ: "JWT"}));
    const payloadStr = btoa(JSON.stringify(payload));
    return header + "." + payloadStr + ".";
}

// 调试信息
console.log("Debug: API Key =", CONFIG.apiKey);
console.log("Debug: Database URL =", CONFIG.databaseUrl);
console.log("Debug: Admin Password =", CONFIG.adminPassword);

// AJAX请求函数
function makeRequest(endpoint, data) {
    return fetch(endpoint, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer ' + localStorage.getItem('token'),
            'X-API-Key': CONFIG.apiKey
        },
        body: JSON.stringify(data)
    });
}

// 用户管理函数
function deleteUser(userId) {
    // 危险的删除操作，没有权限检查
    return makeRequest('/api/users/' + userId + '/delete', {});
}

function backupDatabase() {
    // 数据库备份功能
    window.location.href = '/api/backup/download?token=' + CONFIG.apiSecret;
}`

	w.Header().Set("Content-Type", "application/javascript")
	fmt.Fprint(w, js)
}

// jsConfigHandler JavaScript配置文件处理器
func (lab *VulnLab) jsConfigHandler(w http.ResponseWriter, r *http.Request) {
	js := `// 配置文件 - 包含敏感配置信息
window.APP_CONFIG = {
    // 数据库配置
    database: {
        host: "localhost",
        port: 3306,
        username: "root",
        password: "super_secret_password123",
        database: "testlab"
    },

    // API密钥
    apiKeys: {
        stripe: "sk_live_51234567890abcdef",
        aws: "AKIA1234567890ABCDEF",
        awsSecret: "abcdefghijklmnopqrstuvwxyz1234567890ABCDEF",
        google: "AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI",
        jwt: "jwt_super_secret_key_2023"
    },

    // 管理员信息
    admin: {
        email: "<EMAIL>",
        phone: "13800138000",
        password: "admin123",
        token: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYWRtaW4ifQ.test"
    },

    // 服务器信息
    server: {
        ip: "*************",
        internalIp: "**********",
        sshPassword: "ssh_password_123",
        rootPassword: "root_password_456"
    },

    // 调试模式
    debug: true,
    environment: "development"
};

// 暴露敏感信息到全局
window.API_KEY = window.APP_CONFIG.apiKeys.stripe;
window.DB_PASSWORD = window.APP_CONFIG.database.password;
window.ADMIN_TOKEN = window.APP_CONFIG.admin.token;`

	w.Header().Set("Content-Type", "application/javascript")
	fmt.Fprint(w, js)
}

// jsAPIHandler JavaScript API文件处理器
func (lab *VulnLab) jsAPIHandler(w http.ResponseWriter, r *http.Request) {
	js := `// API接口定义文件
class APIClient {
    constructor() {
        this.baseURL = "https://api.testlab.com";
        this.apiKey = "sk_live_1234567890abcdefghijklmnopqrstuvwxyz";
        this.secret = "your_secret_key_here_12345";
    }

    // 用户相关API
    async getUsers() {
        return this.request('/api/users');
    }

    async getUser(id) {
        return this.request('/api/users/' + id);
    }

    async deleteUser(id) {
        return this.request('/api/users/' + id, 'DELETE');
    }

    // 管理员API
    async getAdminConfig() {
        return this.request('/api/admin/config');
    }

    async updateConfig(config) {
        return this.request('/api/admin/config', 'PUT', config);
    }

    // 敏感操作API
    async backupDatabase() {
        return this.request('/api/backup/create');
    }

    async downloadBackup() {
        window.location.href = '/api/backup/download?secret=' + this.secret;
    }

    // 通用请求方法
    async request(endpoint, method = 'GET', data = null) {
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer ' + this.getToken(),
                'X-API-Key': this.apiKey,
                'X-Secret': this.secret
            }
        };

        if (data) {
            options.body = JSON.stringify(data);
        }

        return fetch(this.baseURL + endpoint, options);
    }

    getToken() {
        // 硬编码的令牌
        return "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJyb2xlIjoiYWRtaW4ifQ.test";
    }
}

// 实例化API客户端
const api = new APIClient();

// 暴露到全局作用域
window.api = api;
window.API_KEY = api.apiKey;
window.API_SECRET = api.secret;

// 调试信息
console.log("API Client initialized");
console.log("API Key:", api.apiKey);
console.log("Secret:", api.secret);`

	w.Header().Set("Content-Type", "application/javascript")
	fmt.Fprint(w, js)
}

// backupDirHandler 备份目录处理器
func (lab *VulnLab) backupDirHandler(w http.ResponseWriter, r *http.Request) {
	html := `
<!DOCTYPE html>
<html>
<head><title>备份目录</title></head>
<body>
    <h1>📦 备份目录</h1>
    <p>这是一个包含敏感备份文件的目录。</p>

    <ul>
        <li><a href="/download?file=database_backup.sql">database_backup.sql</a> (15.6KB)</li>
        <li><a href="/download?file=config_backup.txt">config_backup.txt</a> (2.3KB)</li>
        <li><a href="/download?file=users_backup.csv">users_backup.csv</a> (3.4KB)</li>
        <li><a href="/download?file=passwords_backup.txt">passwords_backup.txt</a> (1.8KB)</li>
        <li><a href="/download?file=api_keys_backup.json">api_keys_backup.json</a> (0.9KB)</li>
    </ul>

    <a href="/">返回首页</a>
</body>
</html>`

	w.Header().Set("Content-Type", "text/html")
	fmt.Fprint(w, html)
}

// envFileHandler .env文件处理器
func (lab *VulnLab) envFileHandler(w http.ResponseWriter, r *http.Request) {
	env := `# 环境配置文件
NODE_ENV=development
DEBUG=true

# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=super_secret_password123
DB_DATABASE=testlab

# API密钥
API_KEY=sk_live_1234567890abcdefghijklmnopqrstuvwxyz
SECRET_KEY=your_secret_key_here_12345
JWT_SECRET=jwt_super_secret_key_2023

# 第三方服务
STRIPE_SECRET_KEY=sk_live_51234567890abcdef
AWS_ACCESS_KEY_ID=AKIA1234567890ABCDEF
AWS_SECRET_ACCESS_KEY=abcdefghijklmnopqrstuvwxyz1234567890ABCDEF
GOOGLE_API_KEY=AIzaSyDdI0hCZtE6vySjMm-WEfRq3CPzqKqqsHI

# 邮件配置
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=email_password_123

# 管理员配置
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=admin123
ADMIN_PHONE=13800138000

# 服务器配置
SERVER_IP=*************
SSH_PASSWORD=ssh_password_123
ROOT_PASSWORD=root_password_456`

	w.Header().Set("Content-Type", "text/plain")
	fmt.Fprint(w, env)
}

// robotsHandler robots.txt处理器
func (lab *VulnLab) robotsHandler(w http.ResponseWriter, r *http.Request) {
	robots := `User-agent: *
Disallow: /admin/
Disallow: /api/
Disallow: /backup/
Disallow: /sensitive/
Disallow: /config/
Disallow: /.env
Disallow: /database/
Disallow: /logs/
Disallow: /private/
Disallow: /secret/

# 敏感目录
Disallow: /phpmyadmin/
Disallow: /wp-admin/
Disallow: /administrator/
Disallow: /management/
Disallow: /control/

# 备份文件
Disallow: *.sql
Disallow: *.bak
Disallow: *.backup
Disallow: *.old

Sitemap: https://testlab.com/sitemap.xml`

	w.Header().Set("Content-Type", "text/plain")
	fmt.Fprint(w, robots)
}

// sitemapHandler sitemap.xml处理器
func (lab *VulnLab) sitemapHandler(w http.ResponseWriter, r *http.Request) {
	sitemap := `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
    <url>
        <loc>https://testlab.com/</loc>
        <lastmod>2024-01-01</lastmod>
        <priority>1.0</priority>
    </url>
    <url>
        <loc>https://testlab.com/admin</loc>
        <lastmod>2024-01-01</lastmod>
        <priority>0.8</priority>
    </url>
    <url>
        <loc>https://testlab.com/api/users</loc>
        <lastmod>2024-01-01</lastmod>
        <priority>0.6</priority>
    </url>
    <url>
        <loc>https://testlab.com/backup/</loc>
        <lastmod>2024-01-01</lastmod>
        <priority>0.4</priority>
    </url>
    <url>
        <loc>https://testlab.com/sensitive/config</loc>
        <lastmod>2024-01-01</lastmod>
        <priority>0.2</priority>
    </url>
</urlset>`

	w.Header().Set("Content-Type", "application/xml")
	fmt.Fprint(w, sitemap)
}

// apiV1UsersHandler API v1用户处理器
func (lab *VulnLab) apiV1UsersHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method == "GET" {
		lab.apiUsersHandler(w, r)
		return
	}

	if r.Method == "POST" {
		var user User
		if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
			http.Error(w, "无效的JSON", http.StatusBadRequest)
			return
		}

		// 插入新用户（没有权限检查）
		_, err := lab.db.Exec("INSERT INTO users (username, email, password, role) VALUES (?, ?, ?, ?)",
			user.Username, user.Email, user.Password, user.Role)
		if err != nil {
			http.Error(w, "创建用户失败", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "success", "message": "用户创建成功"})
	}
}

// apiV2UserHandler API v2单个用户处理器
func (lab *VulnLab) apiV2UserHandler(w http.ResponseWriter, r *http.Request) {
	vars := mux.Vars(r)
	id := vars["id"]

	switch r.Method {
	case "GET":
		row := lab.db.QueryRow("SELECT id, username, email, role FROM users WHERE id = ?", id)
		var user User
		if err := row.Scan(&user.ID, &user.Username, &user.Email, &user.Role); err != nil {
			http.Error(w, "用户不存在", http.StatusNotFound)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(user)

	case "PUT":
		var user User
		if err := json.NewDecoder(r.Body).Decode(&user); err != nil {
			http.Error(w, "无效的JSON", http.StatusBadRequest)
			return
		}

		// 更新用户（没有权限检查）
		_, err := lab.db.Exec("UPDATE users SET username=?, email=?, role=? WHERE id=?",
			user.Username, user.Email, user.Role, id)
		if err != nil {
			http.Error(w, "更新用户失败", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "success", "message": "用户更新成功"})

	case "DELETE":
		// 删除用户（没有权限检查）
		_, err := lab.db.Exec("DELETE FROM users WHERE id = ?", id)
		if err != nil {
			http.Error(w, "删除用户失败", http.StatusInternalServerError)
			return
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(map[string]string{"status": "success", "message": "用户删除成功"})
	}
}

// Start 启动服务器
func (lab *VulnLab) Start(port int) {
	addr := fmt.Sprintf(":%d", port)
	log.Printf("🎯 Pulsex测试靶场启动成功！")
	log.Printf("📍 访问地址: http://localhost%s", addr)
	log.Printf("🔥 包含以下漏洞类型:")
	log.Printf("   - XSS (反射型、存储型、DOM型)")
	log.Printf("   - SQL注入 (登录、搜索、查询)")
	log.Printf("   - 敏感信息泄露")
	log.Printf("   - 未授权访问")
	log.Printf("   - 目录遍历")
	log.Printf("   - JavaScript敏感信息")
	log.Printf("🛑 仅用于安全测试，请勿用于非法用途！")

	log.Fatal(http.ListenAndServe(addr, lab.router))
}

func main() {
	port := 8888
	if len(os.Args) > 1 {
		if p, err := strconv.Atoi(os.Args[1]); err == nil {
			port = p
		}
	}

	lab := NewVulnLab()
	defer lab.db.Close()

	lab.Start(port)
}
