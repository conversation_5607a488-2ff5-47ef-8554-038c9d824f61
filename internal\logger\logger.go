package logger

import (
	"fmt"
	"os"
	"path/filepath"
	"time"

	"github.com/fatih/color"
	"github.com/sirupsen/logrus"
)

var (
	Logger *logrus.Logger
	
	// 颜色定义
	Red    = color.New(color.FgRed).SprintFunc()
	Green  = color.New(color.FgGreen).SprintFunc()
	Yellow = color.New(color.FgYellow).SprintFunc()
	Blue   = color.New(color.FgBlue).SprintFunc()
	Purple = color.New(color.FgMagenta).SprintFunc()
	Cyan   = color.New(color.FgCyan).SprintFunc()
	White  = color.New(color.FgWhite).SprintFunc()
	
	// 带背景色的颜色
	RedBg    = color.New(color.FgWhite, color.BgRed).SprintFunc()
	GreenBg  = color.New(color.FgWhite, color.BgGreen).SprintFunc()
	YellowBg = color.New(color.FgBlack, color.BgYellow).SprintFunc()
	BlueBg   = color.New(color.FgWhite, color.BgBlue).SprintFunc()
)

// CustomFormatter 自定义日志格式化器
type CustomFormatter struct {
	TimestampFormat string
	NoColors        bool
}

// Format 格式化日志输出
func (f *CustomFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	timestamp := entry.Time.Format(f.TimestampFormat)
	
	var levelColor func(...interface{}) string
	var levelText string
	
	if !f.NoColors {
		switch entry.Level {
		case logrus.DebugLevel:
			levelColor = Purple
			levelText = "DEBUG"
		case logrus.InfoLevel:
			levelColor = Blue
			levelText = "INFO"
		case logrus.WarnLevel:
			levelColor = Yellow
			levelText = "WARN"
		case logrus.ErrorLevel:
			levelColor = Red
			levelText = "ERROR"
		case logrus.FatalLevel:
			levelColor = RedBg
			levelText = "FATAL"
		case logrus.PanicLevel:
			levelColor = RedBg
			levelText = "PANIC"
		default:
			levelColor = White
			levelText = "UNKNOWN"
		}
	} else {
		levelColor = func(args ...interface{}) string {
			return fmt.Sprint(args...)
		}
		levelText = entry.Level.String()
	}
	
	// 构建日志消息
	message := fmt.Sprintf("[%s] [%s] %s",
		timestamp,
		levelColor(levelText),
		entry.Message,
	)
	
	// 添加字段信息
	if len(entry.Data) > 0 {
		message += " "
		for key, value := range entry.Data {
			message += fmt.Sprintf("%s=%v ", key, value)
		}
	}
	
	return []byte(message + "\n"), nil
}

// InitLogger 初始化日志系统
func InitLogger(level string, outputDir string, debug bool) error {
	Logger = logrus.New()
	
	// 设置日志级别
	logLevel, err := logrus.ParseLevel(level)
	if err != nil {
		logLevel = logrus.InfoLevel
	}
	Logger.SetLevel(logLevel)
	
	// 设置自定义格式化器
	Logger.SetFormatter(&CustomFormatter{
		TimestampFormat: "2006/01/02 15:04:05",
		NoColors:        false,
	})
	
	// 如果是调试模式，同时输出到文件
	if debug && outputDir != "" {
		logFile := filepath.Join(outputDir, fmt.Sprintf("pulsex_%s.log", 
			time.Now().Format("20060102_150405")))
		
		file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return fmt.Errorf("创建日志文件失败: %v", err)
		}
		
		// 使用多输出
		Logger.SetOutput(file)
	}
	
	return nil
}

// Info 信息日志
func Info(args ...interface{}) {
	if Logger != nil {
		Logger.Info(args...)
	}
}

// Infof 格式化信息日志
func Infof(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Infof(format, args...)
	}
}

// Warn 警告日志
func Warn(args ...interface{}) {
	if Logger != nil {
		Logger.Warn(args...)
	}
}

// Warnf 格式化警告日志
func Warnf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Warnf(format, args...)
	}
}

// Error 错误日志
func Error(args ...interface{}) {
	if Logger != nil {
		Logger.Error(args...)
	}
}

// Errorf 格式化错误日志
func Errorf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Errorf(format, args...)
	}
}

// Debug 调试日志
func Debug(args ...interface{}) {
	if Logger != nil {
		Logger.Debug(args...)
	}
}

// Debugf 格式化调试日志
func Debugf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Debugf(format, args...)
	}
}

// Fatal 致命错误日志
func Fatal(args ...interface{}) {
	if Logger != nil {
		Logger.Fatal(args...)
	}
}

// Fatalf 格式化致命错误日志
func Fatalf(format string, args ...interface{}) {
	if Logger != nil {
		Logger.Fatalf(format, args...)
	}
}

// Success 成功信息（绿色）
func Success(message string) {
	fmt.Printf("[%s] [%s] ✅ %s\n",
		time.Now().Format("2006/01/02 15:04:05"),
		Green("SUCCESS"),
		message)
}

// Warning 警告信息（黄色）
func Warning(message string) {
	fmt.Printf("[%s] [%s] ⚠️ %s\n",
		time.Now().Format("2006/01/02 15:04:05"),
		Yellow("WARNING"),
		message)
}

// ErrorMsg 错误信息（红色）
func ErrorMsg(message string) {
	fmt.Printf("[%s] [%s] ❌ %s\n",
		time.Now().Format("2006/01/02 15:04:05"),
		Red("ERROR"),
		message)
}

// Banner 打印横幅
func Banner() {
	banner := `
    ____        __
   / __ \__  __/ /___ ___  _  __
  / /_/ / / / / / __ ` + "`" + `__ \| |/_/
 / ____/ /_/ / / /_/ / / //>  <
/_/    \__,_/_/\__,_/_/ /_/_/|_|

🚀 Pulsex v1.0.0 - 自动化XSS扫描框架 🚀
`
	fmt.Println(Cyan(banner))
}

// PrintModuleStatus 打印模块状态
func PrintModuleStatus(moduleName string, status bool) {
	statusText := "DISABLED"
	statusColor := Red
	statusEmoji := "❌"
	if status {
		statusText = "ENABLED"
		statusColor = Green
		statusEmoji = "✅"
	}

	fmt.Printf("[%s] [%s] %s module status: [%s] %s\n",
		time.Now().Format("2006/01/02 15:04:05"),
		Blue("Module Status"),
		moduleName,
		statusColor(statusEmoji),
		statusText)
}

// PrintProgress 打印进度信息
func PrintProgress(current, total int, message string) {
	percentage := float64(current) / float64(total) * 100
	progressEmoji := "⏳"
	if percentage >= 100 {
		progressEmoji = "✅"
	} else if percentage >= 50 {
		progressEmoji = "🔄"
	}

	fmt.Printf("[%s] [%s] %s Progress: %.1f%% (%d/%d) - %s\n",
		time.Now().Format("2006/01/02 15:04:05"),
		Cyan("PROGRESS"),
		progressEmoji,
		percentage,
		current,
		total,
		message)
}

// PrintVulnerability 打印漏洞信息
func PrintVulnerability(vulnType, url, payload string, confidence float64) {
	confidenceColor := Yellow
	vulnEmoji := "🚨"
	confidenceEmoji := "⚠️"

	if confidence >= 0.8 {
		confidenceColor = Red
		confidenceEmoji = "🔴"
	} else if confidence >= 0.6 {
		confidenceColor = Yellow
		confidenceEmoji = "🟡"
	} else {
		confidenceColor = Green
		confidenceEmoji = "🟢"
	}

	fmt.Printf("[%s] [%s] %s %s vulnerability found!\n",
		time.Now().Format("2006/01/02 15:04:05"),
		RedBg("VULN"),
		vulnEmoji,
		vulnType)
	fmt.Printf("  🎯 URL: %s\n", url)
	fmt.Printf("  💉 Payload: %s\n", payload)
	fmt.Printf("  %s Confidence: %s\n", confidenceEmoji, confidenceColor(fmt.Sprintf("%.2f", confidence)))
}
