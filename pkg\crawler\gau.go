package crawler

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"strings"
	"sync"
	"time"

	mapset "github.com/deckarep/golang-set/v2"
	"github.com/valyala/fasthttp"
	"github.com/valyala/fasthttp/fasthttpproxy"
	"github.com/sirupsen/logrus"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
	"Pulsex/pkg/utils"
	"github.com/lc/gau/v2/pkg/providers"
	"github.com/lc/gau/v2/runner"
)

// GAUEngine GAU爬虫引擎
type GAUEngine struct {
	config    *config.Config
	name      string
	uaManager *utils.UserAgentManager
}

// NewGAUEngine 创建GAU爬虫引擎
func NewGAUEngine(cfg *config.Config) *GAUEngine {
	return &GAUEngine{
		config:    cfg,
		name:      "<PERSON><PERSON>",
		uaManager: utils.GetGlobalUserAgentManager(),
	}
}

// Crawl 执行爬虫
func (ge *GAUEngine) Crawl(ctx context.Context, target string) (*interfaces.CrawlResult, error) {
	logger.Infof("🔍 GAU开始收集历史URL: %s", target)

	result := &interfaces.CrawlResult{
		URLs:       make([]string, 0),
		Forms:      make([]interfaces.Form, 0),
		JSFiles:    make([]string, 0),
		Parameters: make(map[string]string),
		Cookies:    make([]interfaces.Cookie, 0),
		Duration:   0,
		Source:     "GAU",
	}

	startTime := time.Now()

	// 提取域名
	domain, err := ge.extractDomain(target)
	if err != nil {
		return nil, fmt.Errorf("提取域名失败: %v", err)
	}

	// 使用真实的GAU库收集历史URL
	urls, err := ge.fetchHistoricalURLs(ctx, domain)
	if err != nil {
		return nil, fmt.Errorf("GAU收集失败: %v", err)
	}

	// 处理收集到的URL
	for _, urlStr := range urls {
		if ge.isValidURL(urlStr) {
			result.URLs = append(result.URLs, urlStr)

			// 提取参数
			params := ge.extractParametersFromURL(urlStr)
			for param, paramType := range params {
				result.Parameters[param] = paramType
			}

			// 识别JavaScript文件
			if ge.isJavaScriptFile(urlStr) {
				result.JSFiles = append(result.JSFiles, urlStr)
			}
		}
	}

	duration := time.Since(startTime)
	result.Duration = duration
	logger.Infof("✅ GAU收集完成，耗时: %v，发现 %d 个历史URL",
		duration, len(result.URLs))

	return result, nil
}

// LogrusToLogger 自定义logrus Hook，将logrus日志重定向到我们的logger
type LogrusToLogger struct{}

func (hook *LogrusToLogger) Fire(entry *logrus.Entry) error {
	msg := entry.Message
	fields := entry.Data

	// 构建完整的日志消息
	providerName := ""
	if len(fields) > 0 {
		for key, value := range fields {
			if key == "provider" {
				providerName = fmt.Sprintf("%s", value)
				msg = fmt.Sprintf("[%s] %s", value, msg)
			}
		}
	}

	// 特殊处理fetching消息，将其转换为发现URL的消息
	if strings.Contains(msg, "fetching") && providerName != "" {
		// 这是provider开始工作的消息，我们可以忽略或者简化
		return nil
	}

	// 根据级别输出到我们的logger
	switch entry.Level {
	case logrus.ErrorLevel:
		logger.Errorf("%s", msg)
	case logrus.WarnLevel:
		logger.Warnf("%s", msg)
	case logrus.InfoLevel:
		logger.Infof("%s", msg)
	case logrus.DebugLevel:
		logger.Debugf("%s", msg)
	}

	return nil
}

func (hook *LogrusToLogger) Levels() []logrus.Level {
	return logrus.AllLevels
}

// fetchHistoricalURLs 使用GAU库直接收集历史URL
func (ge *GAUEngine) fetchHistoricalURLs(ctx context.Context, domain string) ([]string, error) {
	// 配置logrus - 禁用默认输出，只使用我们的Hook
	logrus.SetOutput(io.Discard) // 禁用默认输出
	logrus.SetFormatter(&logrus.TextFormatter{
		DisableTimestamp: true,
		DisableColors:    true,
	})

	// 清除现有的hooks，添加自定义Hook
	logrus.StandardLogger().ReplaceHooks(make(logrus.LevelHooks))
	logrus.AddHook(&LogrusToLogger{})

	// 设置logrus日志级别
	if ge.config.Debug {
		logrus.SetLevel(logrus.DebugLevel)
	} else {
		logrus.SetLevel(logrus.WarnLevel) // 只显示警告和错误
	}

	logger.Infof("GAU开始从多个来源收集历史URL")



	// 创建HTTP客户端，支持代理和随机UA
	client := &fasthttp.Client{
		ReadTimeout:  ge.config.Crawler.Timeout,
		WriteTimeout: ge.config.Crawler.Timeout,
	}

	// 注意：fasthttp客户端的User-Agent将通过GAU配置设置

	// 如果设置了代理，配置代理
	if ge.config.Crawler.GAU.Proxy.Enable && ge.config.Crawler.GAU.Proxy.URL != "" {
		proxyURL := ge.config.Crawler.GAU.Proxy.URL
		logger.Infof("使用代理: %s", proxyURL)

		// 解析代理URL
		parsedURL, err := url.Parse(proxyURL)
		if err != nil {
			logger.Errorf("代理URL解析失败: %v", err)
		} else {
			// 根据代理类型配置
			switch parsedURL.Scheme {
			case "http", "https":
				// HTTP代理
				client.Dial = fasthttpproxy.FasthttpHTTPDialer(proxyURL)
				logger.Debugf("配置HTTP代理: %s", proxyURL)
			case "socks5":
				// SOCKS5代理
				client.Dial = fasthttpproxy.FasthttpSocksDialer(proxyURL)
				logger.Debugf("配置SOCKS5代理: %s", proxyURL)
			default:
				logger.Warnf("不支持的代理类型: %s", parsedURL.Scheme)
			}

			// 如果有用户名和密码，添加到URL中
			if ge.config.Crawler.GAU.Proxy.Username != "" && ge.config.Crawler.GAU.Proxy.Password != "" {
				logger.Debugf("使用代理认证")
			}
		}
	}

	// 创建GAU配置
	config := &providers.Config{
		Threads:           uint(ge.config.Crawler.GAU.Threads),
		Timeout:           uint(ge.config.Crawler.GAU.Timeout),
		MaxRetries:        uint(ge.config.Crawler.GAU.Retries),
		IncludeSubdomains: ge.config.Crawler.GAU.IncludeSubdomains,
		RemoveParameters:  ge.config.Crawler.GAU.RemoveParameters,
		Client:            client,
		Providers:         ge.config.Crawler.GAU.Sources,
		Blacklist:         mapset.NewThreadUnsafeSet(ge.config.Crawler.GAU.BlacklistExtensions...),
		Output:            "",
		JSON:              false,
		URLScan: providers.URLScan{
			APIKey: ge.config.Crawler.GAU.URLScan.APIKey,
		},
	}

	if ge.config.Debug {
		logger.Infof("GAU配置: 线程=%d, 超时=%ds, 重试=%d",
			config.Threads, config.Timeout, config.MaxRetries)
		logger.Infof("包含子域名: %v, 移除参数: %v",
			config.IncludeSubdomains, config.RemoveParameters)
		logger.Infof("黑名单扩展名: %d个", len(ge.config.Crawler.GAU.BlacklistExtensions))
		if ge.config.Crawler.GAU.URLScan.APIKey != "" {
			logger.Infof("URLScan API密钥已配置")
		} else {
			logger.Warnf("URLScan API密钥未配置")
		}
	}

	// 创建GAU runner
	gau := new(runner.Runner)

	// 初始化providers
	err := gau.Init(config, ge.config.Crawler.GAU.Sources, providers.Filters{})
	if err != nil {
		return nil, fmt.Errorf("初始化GAU失败: %v", err)
	}

	// 创建结果通道
	results := make(chan runner.URLResult, 1000)
	workChan := make(chan runner.Work, len(gau.Providers))

	// 启动GAU worker
	gau.Start(ctx, workChan, results)

	// 为每个provider创建工作任务
	for _, provider := range gau.Providers {
		logger.Infof("启动 %s provider", provider.Name())
		workChan <- runner.NewWork(domain, provider)
	}
	close(workChan)

	// 收集结果并实时输出
	var urls []string
	var wg sync.WaitGroup
	wg.Add(1)

	go func() {
		defer wg.Done()
		for urlResult := range results {
			if urlResult.URL != "" {
				urls = append(urls, urlResult.URL)
				// 实时输出发现的URL，现在可以显示provider信息了！
				logger.Infof("[%s] 发现URL: %s", urlResult.Provider, urlResult.URL)
			}
		}
	}()

	// 等待所有provider完成
	gau.Wait()
	close(results)

	// 等待结果收集完成
	wg.Wait()

	// 去重处理
	urlSet := mapset.NewThreadUnsafeSet[string]()
	for _, url := range urls {
		urlSet.Add(url)
	}
	urls = urlSet.ToSlice()

	// 限制结果数量
	if len(urls) > ge.config.Crawler.GAU.MaxResultsPerSource {
		urls = urls[:ge.config.Crawler.GAU.MaxResultsPerSource]
	}

	logger.Infof("GAU收集完成，共收集到 %d 个URL", len(urls))
	return urls, nil
}



// extractParametersFromURL 从URL中提取参数
func (ge *GAUEngine) extractParametersFromURL(urlStr string) map[string]string {
	params := make(map[string]string)
	
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return params
	}

	for param, values := range parsedURL.Query() {
		if len(values) > 0 {
			// 尝试推断参数类型
			paramType := ge.inferParameterType(values[0])
			params[param] = paramType
		}
	}

	return params
}

// inferParameterType 推断参数类型
func (ge *GAUEngine) inferParameterType(value string) string {
	// 简单的类型推断
	if value == "" {
		return "string"
	}

	// 检查是否为数字
	if ge.isNumeric(value) {
		return "integer"
	}

	// 检查是否为邮箱
	if ge.isEmail(value) {
		return "email"
	}

	// 检查是否为URL
	if ge.isURL(value) {
		return "url"
	}

	// 默认为字符串
	return "string"
}

// isNumeric 检查字符串是否为数字
func (ge *GAUEngine) isNumeric(s string) bool {
	for _, char := range s {
		if char < '0' || char > '9' {
			return false
		}
	}
	return len(s) > 0
}

// isEmail 检查字符串是否为邮箱格式
func (ge *GAUEngine) isEmail(s string) bool {
	return strings.Contains(s, "@") && strings.Contains(s, ".")
}

// isURL 检查字符串是否为URL格式
func (ge *GAUEngine) isURL(s string) bool {
	return strings.HasPrefix(s, "http://") || strings.HasPrefix(s, "https://")
}

// isJavaScriptFile 检查URL是否为JavaScript文件
func (ge *GAUEngine) isJavaScriptFile(urlStr string) bool {
	return strings.HasSuffix(strings.ToLower(urlStr), ".js")
}

// isValidURL 验证URL是否有效
func (ge *GAUEngine) isValidURL(urlStr string) bool {
	if urlStr == "" {
		return false
	}

	// 解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return false
	}

	// 检查协议
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return false
	}

	// 检查主机
	if parsedURL.Host == "" {
		return false
	}

	// 过滤一些不需要的文件类型
	excludeExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".ico",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".zip", ".rar", ".tar", ".gz", ".7z",
		".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv",
		".css", ".woff", ".woff2", ".ttf", ".eot",
	}

	path := strings.ToLower(parsedURL.Path)
	for _, ext := range excludeExtensions {
		if strings.HasSuffix(path, ext) {
			return false
		}
	}

	return true
}

// extractDomain 提取域名
func (ge *GAUEngine) extractDomain(target string) (string, error) {
	parsedURL, err := url.Parse(target)
	if err != nil {
		return "", err
	}

	domain := parsedURL.Host
	
	// 移除端口号
	if colonIndex := strings.Index(domain, ":"); colonIndex != -1 {
		domain = domain[:colonIndex]
	}

	// 移除www前缀
	if strings.HasPrefix(domain, "www.") {
		domain = domain[4:]
	}

	return domain, nil
}

// SetConfig 设置配置
func (ge *GAUEngine) SetConfig(cfg interface{}) error {
	if c, ok := cfg.(*config.Config); ok {
		ge.config = c
		return nil
	}
	return fmt.Errorf("无效的配置类型")
}

// GetName 获取爬虫名称
func (ge *GAUEngine) GetName() string {
	return ge.name
}







// GAUStats GAU统计信息
type GAUStats struct {
	TotalURLs        int           `json:"total_urls"`
	UniqueURLs       int           `json:"unique_urls"`
	ParametersFound  int           `json:"parameters_found"`
	JSFilesFound     int           `json:"js_files_found"`
	SourcesQueried   []string      `json:"sources_queried"`
	CollectionTime   time.Duration `json:"collection_time"`
}

// GetStats 获取GAU统计信息
func (ge *GAUEngine) GetStats() *GAUStats {
	return &GAUStats{
		TotalURLs:       0,
		UniqueURLs:      0,
		ParametersFound: 0,
		JSFilesFound:    0,
		SourcesQueried:  ge.config.Crawler.GAU.Sources,
		CollectionTime:  0,
	}
}

// validateGAUDomain 验证GAU域名
func (ge *GAUEngine) validateGAUDomain(domain string) error {
	if domain == "" {
		return fmt.Errorf("域名不能为空")
	}

	// 检查域名格式
	if strings.Contains(domain, "://") {
		return fmt.Errorf("域名不应包含协议")
	}

	if strings.Contains(domain, "/") {
		return fmt.Errorf("域名不应包含路径")
	}

	return nil
}
