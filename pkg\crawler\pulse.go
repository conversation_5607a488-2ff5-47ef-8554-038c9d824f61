package crawler

import (
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"sync"
	"time"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
	"Pulsex/pkg/utils"
)

// PulseEngine Pulse自实现爬虫引擎
type PulseEngine struct {
	config    *config.Config
	results   *PulseResults
	mu        sync.RWMutex
	uaManager *utils.UserAgentManager
}

// PulseResults Pulse爬虫结果
type PulseResults struct {
	URLs        []string          `json:"urls"`
	Forms       []FormInfo        `json:"forms"`
	JSFiles     []string          `json:"js_files"`
	Parameters  map[string]string `json:"parameters"`
	Endpoints   []string          `json:"endpoints"`
	Subdomains  []string          `json:"subdomains"`
	Technologies []string         `json:"technologies"`
}

// FormInfo 表单信息
type FormInfo struct {
	Action     string            `json:"action"`
	Method     string            `json:"method"`
	Fields     []FormField       `json:"fields"`
	URL        string            `json:"url"`
}

// FormField 表单字段
type FormField struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Value       string `json:"value"`
	Placeholder string `json:"placeholder"`
}

// NewPulseEngine 创建新的Pulse爬虫引擎
func NewPulseEngine(cfg *config.Config) *PulseEngine {
	return &PulseEngine{
		config:    cfg,
		uaManager: utils.GetGlobalUserAgentManager(),
		results: &PulseResults{
			URLs:         make([]string, 0),
			Forms:        make([]FormInfo, 0),
			JSFiles:      make([]string, 0),
			Parameters:   make(map[string]string),
			Endpoints:    make([]string, 0),
			Subdomains:   make([]string, 0),
			Technologies: make([]string, 0),
		},
	}
}

// CrawlWithSeeds 使用种子URL进行Pulse爬取
func (pe *PulseEngine) CrawlWithSeeds(ctx context.Context, seedURLs []string) (*interfaces.CrawlResult, error) {
	if len(seedURLs) == 0 {
		return nil, fmt.Errorf("没有提供种子URL")
	}

	logger.Infof("🕸️ Pulse爬虫开始爬取，种子URL数量: %d", len(seedURLs))
	startTime := time.Now()

	// 去重种子URL
	uniqueSeeds := pe.deduplicateURLs(seedURLs)
	logger.Infof("去重后种子URL数量: %d", len(uniqueSeeds))

	// 分类URL
	normalURLs, highValueURLs := pe.categorizeURLs(uniqueSeeds)

	// 创建爬虫选项
	options := pe.createCrawlOptions()

	// 爬取普通URL
	if len(normalURLs) > 0 {
		logger.Infof("🔍 开始爬取普通URL (%d个)", len(normalURLs))
		err := pe.crawlNormalURLs(ctx, normalURLs, options)
		if err != nil {
			logger.Warnf("普通URL爬取失败: %v", err)
		}
	}

	// 爬取高价值URL
	if len(highValueURLs) > 0 {
		logger.Infof("🎯 开始爬取高价值URL (%d个)", len(highValueURLs))
		err := pe.crawlHighValueURLs(ctx, highValueURLs, options)
		if err != nil {
			logger.Warnf("高价值URL爬取失败: %v", err)
		}
	}

	duration := time.Since(startTime)
	logger.Infof("✅ Pulse爬虫爬取完成，耗时: %v", duration)

	// 转换结果
	result := pe.convertResults(duration)
	return result, nil
}

// createCrawlOptions 创建爬虫选项
func (pe *PulseEngine) createCrawlOptions() map[string]interface{} {
	options := map[string]interface{}{
		"max_depth":   5,
		"concurrency": 10,
		"timeout":     10,
	}

	// 从配置中覆盖参数
	if pe.config.Crawler.Pulse.MaxDepth > 0 {
		options["max_depth"] = pe.config.Crawler.Pulse.MaxDepth
	}
	if pe.config.Crawler.Pulse.Concurrency > 0 {
		options["concurrency"] = pe.config.Crawler.Pulse.Concurrency
	}
	if pe.config.Crawler.Pulse.Timeout > 0 {
		options["timeout"] = pe.config.Crawler.Pulse.Timeout
	}

	return options
}

// deduplicateURLs 去重URL
func (pe *PulseEngine) deduplicateURLs(urls []string) []string {
	seen := make(map[string]bool)
	var unique []string

	for _, u := range urls {
		// 标准化URL
		normalized := pe.normalizeURL(u)
		if normalized != "" && !seen[normalized] {
			seen[normalized] = true
			unique = append(unique, normalized)
		}
	}

	return unique
}

// normalizeURL 标准化URL
func (pe *PulseEngine) normalizeURL(rawURL string) string {
	if rawURL == "" {
		return ""
	}

	// 确保URL有协议�?
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "http://" + rawURL
	}

	// 解析URL
	parsedURL, err := url.Parse(rawURL)
	if err != nil {
		return ""
	}

	// 移除fragment
	parsedURL.Fragment = ""

	return parsedURL.String()
}

// categorizeURLs 增强的URL分类
func (pe *PulseEngine) categorizeURLs(urls []string) ([]string, []string) {
	var normalURLs []string
	var highValueURLs []string

	for _, u := range urls {
		score := pe.calculateURLValue(u)
		if score >= 50 { // 高价值阈值�?			highValueURLs = append(highValueURLs, u)
			logger.Debugf("[pulse] 🎯 高价值URL (分数:%d): %s", score, u)
		} else {
			normalURLs = append(normalURLs, u)
		}
	}

	return normalURLs, highValueURLs
}

// calculateURLValue 计算URL价值分数�?
func (pe *PulseEngine) calculateURLValue(url string) int {
	score := 0
	lowerURL := strings.ToLower(url)

	// 管理和认证相�?(高分)
	adminPatterns := map[string]int{
		"/admin":     80,
		"/login":     70,
		"/auth":      70,
		"/signin":    65,
		"/signup":    60,
		"/register":  60,
		"/dashboard": 75,
		"/panel":     70,
		"/console":   75,
	}

	// API和接�?(高分)
	apiPatterns := map[string]int{
		"/api/":      60,
		"/v1/":       50,
		"/v2/":       50,
		"/v3/":       50,
		"/rest/":     55,
		"/graphql":   70,
		"/rpc":       55,
		"/service":   50,
		"/endpoint":  50,
	}

	// 敏感功能 (高分)
	sensitivePatterns := map[string]int{
		"/upload":    75,
		"/download":  60,
		"/file":      55,
		"/backup":    80,
		"/config":    85,
		"/settings":  70,
		"/debug":     90,
		"/test":      60,
		"/dev":       70,
		"/staging":   65,
		"/internal":  80,
		"/private":   85,
	}

	// 计算各类模式分数
	for pattern, points := range adminPatterns {
		if strings.Contains(lowerURL, pattern) {
			score += points
		}
	}

	for pattern, points := range apiPatterns {
		if strings.Contains(lowerURL, pattern) {
			score += points
		}
	}

	for pattern, points := range sensitivePatterns {
		if strings.Contains(lowerURL, pattern) {
			score += points
		}
	}

	// 参数相关 (加分)
	if strings.Contains(url, "?") {
		score += 20 // 有参数的URL更有价值
	}

	// 确保分数不为负
	if score < 0 {
		score = 0
	}

	return score
}

// crawlNormalURLs 爬取普通URL
func (pe *PulseEngine) crawlNormalURLs(ctx context.Context, urls []string, options map[string]interface{}) error {
	maxDepth := 3 // 降低深度避免过度爬取
	if depth, ok := options["max_depth"].(int); ok {
		maxDepth = depth
	}

	// 使用全局visited映射避免重复爬取
	visited := make(map[string]bool)

	for _, targetURL := range urls {
		err := pe.performDeepCrawl(ctx, targetURL, maxDepth, visited)
		if err != nil {
			logger.Warnf("[pulse] 普通URL爬取失败 %s: %v", targetURL, err)
		}
	}

	return nil
}

// crawlHighValueURLs 爬取高价值URL
func (pe *PulseEngine) crawlHighValueURLs(ctx context.Context, urls []string, options map[string]interface{}) error {
	maxDepth := 2 // 高价值URL使用更小深度
	if depth, ok := options["max_depth"].(int); ok && depth > 0 {
		maxDepth = depth - 3 // 比普通URL少3层深度
	}

	// 使用全局visited映射避免重复爬取
	visited := make(map[string]bool)

	for _, targetURL := range urls {
		err := pe.performDeepCrawl(ctx, targetURL, maxDepth, visited)
		if err != nil {
			logger.Warnf("[pulse] 高价值URL爬取失败 %s: %v", targetURL, err)
		}
	}

	return nil
}

// performDeepCrawl 执行深度爬取
func (pe *PulseEngine) performDeepCrawl(ctx context.Context, targetURL string, maxDepth int, visited map[string]bool) error {
	if maxDepth <= 0 {
		return nil
	}

	// 检查是否已访问
	if visited[targetURL] {
		return nil
	}
	visited[targetURL] = true

	logger.Debugf("[pulse] 爬取深度 %d: %s", maxDepth, targetURL)

	// 发送HTTP请求
	resp, err := pe.fetchURL(ctx, targetURL)
	if err != nil {
		logger.Warnf("[pulse] 请求失败 %s: %v", targetURL, err)
		return nil
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		logger.Warnf("[pulse] 读取响应失败 %s: %v", targetURL, err)
		return nil
	}

	// 记录发现的URL
	pe.mu.Lock()
	pe.results.URLs = append(pe.results.URLs, targetURL)
	logger.Infof("[pulse] 发现URL: %s", targetURL)

	// 检查是否为JS文件
	if strings.HasSuffix(targetURL, ".js") {
		pe.results.JSFiles = append(pe.results.JSFiles, targetURL)
		logger.Infof("[pulse] 发现JS文件: %s", targetURL)
	}
	pe.mu.Unlock()

	// 解析内容中的链接
	var links []string
	if strings.HasSuffix(targetURL, ".js") {
		// JavaScript文件特殊处理
		links = pe.extractJSEndpoints(string(body), targetURL)
	} else {
		// HTML文件处理
		links = pe.extractLinks(string(body), targetURL)
		// 同时检测表单
		pe.detectAndProcessForms(string(body), targetURL)
	}

	// 递归爬取发现的链接
	for _, link := range links {
		if !visited[link] && pe.shouldCrawl(link, targetURL) {
			err := pe.performDeepCrawl(ctx, link, maxDepth-1, visited)
			if err != nil {
				logger.Debugf("[pulse] 递归爬取失败 %s: %v", link, err)
			}
		}
	}

	return nil
}

// fetchURL 发送HTTP请求
func (pe *PulseEngine) fetchURL(ctx context.Context, targetURL string) (*http.Response, error) {
	// 使用全局TLS配置
	client := &http.Client{
		Timeout:   10 * time.Second,
		Transport: utils.GetDefaultTransport(),
		CheckRedirect: func(req *http.Request, via []*http.Request) error {
			if len(via) >= 3 {
				return http.ErrUseLastResponse
			}
			return nil
		},
	}

	req, err := http.NewRequestWithContext(ctx, "GET", targetURL, nil)
	if err != nil {
		return nil, err
	}

	// 设置统一随机User-Agent
	if pe.config.Crawler.RandomUserAgent.Enable {
		req.Header.Set("User-Agent", utils.GetRandomUserAgentFromGlobal())
	} else {
		req.Header.Set("User-Agent", utils.GetDefaultUserAgent())
	}
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "en-US,en;q=0.9")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("DNT", "1")
	req.Header.Set("Connection", "keep-alive")
	req.Header.Set("Upgrade-Insecure-Requests", "1")

	// 设置自定义请求头
	pe.setCustomHeaders(req)

	return client.Do(req)
}

// setCustomHeaders 设置自定义请求头
func (pe *PulseEngine) setCustomHeaders(req *http.Request) {
	// 设置自定义Headers
	for key, value := range pe.config.Crawler.Pulse.Headers {
		req.Header.Set(key, value)
		logger.Debugf("[pulse] 设置自定义请求头: %s = %s", key, value)
	}

	// 设置Cookies
	if pe.config.Crawler.Pulse.Cookies != "" {
		req.Header.Set("Cookie", pe.config.Crawler.Pulse.Cookies)
		logger.Debugf("[pulse] 设置Cookie: %s", pe.config.Crawler.Pulse.Cookies)
	}
}

// extractLinks 从HTML中提取链接
func (pe *PulseEngine) extractLinks(htmlContent, baseURL string) []string {
	var links []string

	// 简单的正则表达式提取链接
	linkPatterns := []string{
		`href\s*=\s*["']([^"']+)["']`,
		`src\s*=\s*["']([^"']+)["']`,
		`action\s*=\s*["']([^"']+)["']`,
	}

	for _, pattern := range linkPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(htmlContent, -1)

		for _, match := range matches {
			if len(match) > 1 {
				link := pe.resolveURL(match[1], baseURL)
				if link != "" {
					links = append(links, link)
				}
			}
		}
	}

	// 去重
	return pe.deduplicateURLs(links)
}

// extractJSEndpoints 从JavaScript文件中提取API端点
func (pe *PulseEngine) extractJSEndpoints(jsContent, baseURL string) []string {
	var endpoints []string

	logger.Infof("[pulse-js] 🔍 分析JavaScript文件: %s", baseURL)

	// JavaScript API调用模式
	jsPatterns := []string{
		// fetch API
		`fetch\s*\(\s*["'\x60]([^"'\x60]+)["'\x60]`,
		// axios
		`axios\.get\s*\(\s*["'\x60]([^"'\x60]+)["'\x60]`,
		`axios\.post\s*\(\s*["'\x60]([^"'\x60]+)["'\x60]`,
		// jQuery AJAX
		`\$\.ajax\s*\(\s*\{[^}]*url\s*:\s*["'\x60]([^"'\x60]+)["'\x60]`,
		`\$\.get\s*\(\s*["'\x60]([^"'\x60]+)["'\x60]`,
		// 一般URL模式
		`["'\x60](/api/[^"'\x60]+)["'\x60]`,
		`["'\x60](/v\d+/[^"'\x60]+)["'\x60]`,
	}

	for _, pattern := range jsPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindAllStringSubmatch(jsContent, -1)

		for _, match := range matches {
			if len(match) > 1 {
				endpoint := match[1]
				// 解析为完整URL
				fullURL := pe.resolveURL(endpoint, baseURL)
				if fullURL != "" && pe.isValidAPIEndpoint(fullURL) {
					endpoints = append(endpoints, fullURL)
					logger.Infof("[pulse-js] 🎯 发现API端点: %s", fullURL)
				}
			}
		}
	}

	return pe.deduplicateURLs(endpoints)
}

// isValidAPIEndpoint 判断是否为有效的API端点
func (pe *PulseEngine) isValidAPIEndpoint(url string) bool {
	// API端点特征
	apiIndicators := []string{
		"/api/", "/v1/", "/v2/", "/v3/", "/rest/", "/graphql",
		"/json", "/xml", "/rpc", "/service", "/endpoint",
	}

	lowerURL := strings.ToLower(url)
	for _, indicator := range apiIndicators {
		if strings.Contains(lowerURL, indicator) {
			return true
		}
	}

	return false
}

// resolveURL 解析相对URL为绝对URL
func (pe *PulseEngine) resolveURL(href, baseURL string) string {
	if href == "" {
		return ""
	}

	// 跳过JavaScript和邮件链接
	if strings.HasPrefix(href, "javascript:") || strings.HasPrefix(href, "mailto:") {
		return ""
	}

	// 解析基础URL
	base, err := url.Parse(baseURL)
	if err != nil {
		return ""
	}

	// 解析href
	ref, err := url.Parse(href)
	if err != nil {
		return ""
	}

	// 解析为绝对URL
	resolved := base.ResolveReference(ref)
	return resolved.String()
}

// shouldCrawl 判断是否应该爬取该URL
func (pe *PulseEngine) shouldCrawl(targetURL, baseURL string) bool {
	// 解析URL
	target, err := url.Parse(targetURL)
	if err != nil {
		return false
	}

	base, err := url.Parse(baseURL)
	if err != nil {
		return false
	}

	// 只爬取同域名的URL
	if target.Host != base.Host {
		return false
	}

	// 跳过某些文件类型
	skipExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".zip", ".rar", ".tar", ".gz", ".mp3", ".mp4", ".avi",
		".css", ".ico", ".woff", ".woff2", ".ttf", ".eot",
	}

	path := strings.ToLower(target.Path)
	for _, ext := range skipExtensions {
		if strings.HasSuffix(path, ext) {
			return false
		}
	}

	return true
}

// detectAndProcessForms 检测和处理表单
func (pe *PulseEngine) detectAndProcessForms(htmlContent, pageURL string) {
	// 简单的表单检测
	formPattern := regexp.MustCompile(`<form[^>]*>.*?</form>`)
	forms := formPattern.FindAllString(htmlContent, -1)

	for _, formHTML := range forms {
		form := pe.parseForm(formHTML, pageURL)
		if form.Action != "" {
			pe.mu.Lock()
			pe.results.Forms = append(pe.results.Forms, form)
			pe.mu.Unlock()
			logger.Infof("[pulse] 📝 发现表单: %s", form.Action)
		}
	}
}

// parseForm 解析表单
func (pe *PulseEngine) parseForm(formHTML, pageURL string) FormInfo {
	form := FormInfo{
		Method: "GET", // 默认方法
		Fields: make([]FormField, 0),
		URL:    pageURL,
	}

	// 提取action
	actionPattern := regexp.MustCompile(`action\s*=\s*["']([^"']+)["']`)
	if match := actionPattern.FindStringSubmatch(formHTML); len(match) > 1 {
		form.Action = pe.resolveURL(match[1], pageURL)
	}

	// 提取method
	methodPattern := regexp.MustCompile(`method\s*=\s*["']([^"']+)["']`)
	if match := methodPattern.FindStringSubmatch(formHTML); len(match) > 1 {
		form.Method = strings.ToUpper(match[1])
	}

	// 提取输入字段
	inputPattern := regexp.MustCompile(`<input[^>]*>`)
	inputs := inputPattern.FindAllString(formHTML, -1)

	for _, input := range inputs {
		field := pe.parseInputField(input)
		if field.Name != "" {
			form.Fields = append(form.Fields, field)
		}
	}

	return form
}

// parseInputField 解析输入字段
func (pe *PulseEngine) parseInputField(inputHTML string) FormField {
	field := FormField{}

	// 提取name
	namePattern := regexp.MustCompile(`name\s*=\s*["']([^"']+)["']`)
	if match := namePattern.FindStringSubmatch(inputHTML); len(match) > 1 {
		field.Name = match[1]
	}

	// 提取type
	typePattern := regexp.MustCompile(`type\s*=\s*["']([^"']+)["']`)
	if match := typePattern.FindStringSubmatch(inputHTML); len(match) > 1 {
		field.Type = match[1]
	} else {
		field.Type = "text" // 默认类型
	}

	// 提取value
	valuePattern := regexp.MustCompile(`value\s*=\s*["']([^"']+)["']`)
	if match := valuePattern.FindStringSubmatch(inputHTML); len(match) > 1 {
		field.Value = match[1]
	}

	// 提取placeholder
	placeholderPattern := regexp.MustCompile(`placeholder\s*=\s*["']([^"']+)["']`)
	if match := placeholderPattern.FindStringSubmatch(inputHTML); len(match) > 1 {
		field.Placeholder = match[1]
	}

	return field
}

// convertResults 转换结果格式
func (pe *PulseEngine) convertResults(duration time.Duration) *interfaces.CrawlResult {
	pe.mu.RLock()
	defer pe.mu.RUnlock()

	// 转换表单格式
	var forms []interfaces.Form
	for _, form := range pe.results.Forms {
		fields := make(map[string]string)
		for _, field := range form.Fields {
			fields[field.Name] = field.Value
		}

		forms = append(forms, interfaces.Form{
			Action: form.Action,
			Method: form.Method,
			Fields: fields,
		})
	}

	return &interfaces.CrawlResult{
		URLs:       pe.results.URLs,
		JSFiles:    pe.results.JSFiles,
		Parameters: pe.results.Parameters,
		Forms:      forms,
		Cookies:    []interfaces.Cookie{}, // Pulse不收集Cookie
		Duration:   duration,
		Source:     "Pulse",
	}
}

// GetName 获取引擎名称
func (pe *PulseEngine) GetName() string {
	return "Pulse"
}

// SetConfig 设置配置
func (pe *PulseEngine) SetConfig(cfg interface{}) error {
	if c, ok := cfg.(*config.Config); ok {
		pe.config = c
		return nil
	}
	return fmt.Errorf("无效的配置类型")
}

// Crawl 实现Crawler接口
func (pe *PulseEngine) Crawl(ctx context.Context, target string) (*interfaces.CrawlResult, error) {
	return pe.CrawlWithSeeds(ctx, []string{target})
}
