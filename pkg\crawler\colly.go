package crawler

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
	"math/rand"

	"github.com/gocolly/colly/v2"
	"github.com/gocolly/colly/v2/debug"
	"github.com/gocolly/colly/v2/extensions"
	"github.com/gocolly/colly/v2/proxy"
	"github.com/gocolly/colly/v2/storage"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
	"Pulsex/pkg/utils"
)

// CollyEngine Colly爬虫引擎
type CollyEngine struct {
	config    *config.Config
	collector *colly.Collector
	name      string
	validURLs map[string]bool  // 跟踪有效的URL
	failedURLs map[string]bool // 跟踪失败的URL
	uaManager  *utils.UserAgentManager
}

// NewCollyEngine 创建Colly爬虫引擎
func NewCollyEngine(cfg *config.Config) *CollyEngine {
	// 创建收集器选项
	var options []colly.CollectorOption

	// 设置异步模式
	if cfg.Crawler.Colly.Async {
		options = append(options, colly.Async(true))
	}

	// 设置用户代理（统一随机UA优先）
	if cfg.Crawler.RandomUserAgent.Enable {
		// 使用全局随机UA，设置一个初始UA避免默认值
		options = append(options, colly.UserAgent(utils.GetRandomUserAgentFromGlobal()))
		logger.Debugf("使用全局随机User-Agent")
	} else if !cfg.Crawler.Colly.Request.RandomUserAgent {
		// 使用固定UA
		userAgent := cfg.Crawler.Colly.Request.UserAgent
		if userAgent == "" {
			userAgent = cfg.Crawler.UserAgent
		}
		if userAgent != "" {
			options = append(options, colly.UserAgent(userAgent))
		}
	}

	// 创建收集器
	c := colly.NewCollector(options...)

	engine := &CollyEngine{
		config:     cfg,
		collector:  c,
		name:       "Colly",
		validURLs:  make(map[string]bool),
		failedURLs: make(map[string]bool),
		uaManager:  utils.GetGlobalUserAgentManager(),
	}

	// 配置收集器
	engine.configureCollyCollector()

	return engine
}

// configureCollyCollector 配置Colly收集器
func (ce *CollyEngine) configureCollyCollector() {
	c := ce.collector
	collyConfig := ce.config.Crawler.Colly

	// 使用全局TLS配置
	c.WithTransport(utils.GetDefaultTransport())

	// 统一设置随机用户代理
	if ce.config.Crawler.RandomUserAgent.Enable {
		c.OnRequest(func(r *colly.Request) {
			r.Headers.Set("User-Agent", utils.GetRandomUserAgentFromGlobal())
		})
		logger.Infof("启用统一随机用户代理")
	}

	// 设置超时
	c.SetRequestTimeout(collyConfig.Request.Timeout)

	// 设置速率限制
	if collyConfig.RateLimit.Enable {
		delay := collyConfig.RateLimit.Delay
		if collyConfig.RateLimit.RandomizeDelay {
			// 随机化延迟 (50%-150%的原始延迟)
			minDelay := time.Duration(float64(delay) * 0.5)
			maxDelay := time.Duration(float64(delay) * 1.5)
			delay = minDelay + time.Duration(rand.Int63n(int64(maxDelay-minDelay)))
		}

		c.Limit(&colly.LimitRule{
			DomainGlob:  "*",
			Parallelism: collyConfig.RateLimit.Parallelism,
			Delay:       delay,
		})
	}

	// 设置代理
	if collyConfig.Proxy.Enable && collyConfig.Proxy.URL != "" {
		proxyURL := collyConfig.Proxy.URL

		// 如果有用户名和密码，添加到URL中
		if collyConfig.Proxy.Username != "" && collyConfig.Proxy.Password != "" {
			if parsedURL, err := url.Parse(proxyURL); err == nil {
				parsedURL.User = url.UserPassword(collyConfig.Proxy.Username, collyConfig.Proxy.Password)
				proxyURL = parsedURL.String()
			}
		}

		rp, err := proxy.RoundRobinProxySwitcher(proxyURL)
		if err != nil {
			logger.Errorf("代理配置失败: %v", err)
		} else {
			c.SetProxyFunc(rp)
			logger.Infof("使用代理: %s", collyConfig.Proxy.URL)
		}
	}

	// 随机用户代理已在前面设置

	// 设置Referer
	extensions.Referer(c)

	// 设置全局自定义请求头
	c.OnRequest(func(r *colly.Request) {
		// 先设置全局Headers
		for key, value := range ce.config.Crawler.Headers {
			r.Headers.Set(key, value)
			logger.Debugf("[colly] 设置全局自定义请求头: %s = %s", key, value)
		}

		// 再设置Colly特定的Headers（会覆盖全局配置）
		for key, value := range collyConfig.Headers {
			r.Headers.Set(key, value)
			logger.Debugf("[colly] 设置Colly自定义请求头: %s = %s", key, value)
		}

		// 设置全局Cookies
		if ce.config.Crawler.Cookies != "" {
			r.Headers.Set("Cookie", ce.config.Crawler.Cookies)
			logger.Debugf("[colly] 设置全局Cookie: %s", ce.config.Crawler.Cookies)
		}
	})

	// 设置Cookie Jar
	if collyConfig.Cookies.Enable && collyConfig.Cookies.Jar {
		// Colly默认已经有Cookie支持，这里只是确保启用
		logger.Debugf("启用Cookie支持")
	}

	// 设置域名过滤
	if len(collyConfig.Filters.AllowedDomains) > 0 {
		c.AllowedDomains = collyConfig.Filters.AllowedDomains
	}
	if len(collyConfig.Filters.DisallowedDomains) > 0 {
		c.DisallowedDomains = collyConfig.Filters.DisallowedDomains
	}

	// 设置URL过滤
	if len(collyConfig.Filters.URLFilters) > 0 {
		for _, filter := range collyConfig.Filters.URLFilters {
			if re, err := regexp.Compile(filter); err == nil {
				c.URLFilters = append(c.URLFilters, re)
			} else {
				logger.Warnf("无效的URL过滤正则表达式: %s", filter)
			}
		}
	}

	// 设置缓存
	if collyConfig.Cache.Enable {
		c.SetStorage(&storage.InMemoryStorage{})
		logger.Infof("启用Colly缓存: %s", collyConfig.Cache.Dir)
	}

	// 设置调试模式
	if collyConfig.Debug.Enable {
		c.SetDebugger(&debug.LogDebugger{})
		logger.Infof("启用Colly调试模式")
	}

	// 设置重定向策略
	if !collyConfig.Request.FollowRedirects {
		c.SetRedirectHandler(func(req *http.Request, via []*http.Request) error {
			return http.ErrUseLastResponse
		})
	}
}

// Crawl 执行爬虫
func (ce *CollyEngine) Crawl(ctx context.Context, target string) (*interfaces.CrawlResult, error) {
	logger.Infof("🕷️ Colly开始爬取: %s", target)

	result := &interfaces.CrawlResult{
		URLs:       make([]string, 0),
		Forms:      make([]interfaces.Form, 0),
		JSFiles:    make([]string, 0),
		Parameters: make(map[string]string),
		Cookies:    make([]interfaces.Cookie, 0),
		Duration:   0,
		Source:     "Colly",
	}

	// 解析目标URL
	baseURL, err := url.Parse(target)
	if err != nil {
		return nil, fmt.Errorf("解析URL失败: %v", err)
	}

	// 创建新的collector实例避免冲突
	c := ce.collector.Clone()

	// 设置允许的域名（如果配置中没有指定）
	if len(ce.config.Crawler.Colly.Filters.AllowedDomains) == 0 && baseURL.Host != "" {
		// 检查是否为IP地址
		if strings.Contains(baseURL.Host, ".") && !strings.Contains(baseURL.Host, ":") {
			// 可能是域名，设置域名限制
			c.AllowedDomains = []string{baseURL.Host}
		} else if strings.Contains(baseURL.Host, ":") {
			// 包含端口的情况
			host := strings.Split(baseURL.Host, ":")[0]
			c.AllowedDomains = []string{baseURL.Host, host}
		}
		// 对于IP地址，不设置AllowedDomains限制
		logger.Debugf("目标主机: %s", baseURL.Host)
	}

	// 设置最大深度
	if ce.config.Crawler.Colly.MaxDepth > 0 {
		c.MaxDepth = ce.config.Crawler.Colly.MaxDepth
	}

	// 重置URL跟踪映射
	ce.validURLs = make(map[string]bool)
	ce.failedURLs = make(map[string]bool)

	// 添加请求成功回调
	c.OnResponse(func(r *colly.Response) {
		ce.validURLs[r.Request.URL.String()] = true
		logger.Debugf("[colly] 请求成功: %s (状态码: %d)", r.Request.URL.String(), r.StatusCode)
	})

	// 添加请求失败回调
	c.OnError(func(r *colly.Response, err error) {
		ce.failedURLs[r.Request.URL.String()] = true
		logger.Warnf("[colly] 请求失败: %s (错误: %v)", r.Request.URL.String(), err)
	})

	// 收集链接
	c.OnHTML("a[href]", func(e *colly.HTMLElement) {
		link := e.Attr("href")
		absoluteURL := e.Request.AbsoluteURL(link)
		if absoluteURL != "" && isValidURL(absoluteURL) {
			result.URLs = append(result.URLs, absoluteURL)

			// 检查是否为JS文件
			if strings.HasSuffix(absoluteURL, ".js") {
				result.JSFiles = append(result.JSFiles, absoluteURL)
				logger.Infof("[colly] 发现JS文件: %s", absoluteURL)
			} else {
				// 实时输出发现的URL
				logger.Infof("[colly] 发现URL: %s", absoluteURL)
			}
		}
	})

	// 收集表单
	c.OnHTML("form", func(e *colly.HTMLElement) {
		form := interfaces.Form{
			Action: e.Attr("action"),
			Method: strings.ToUpper(e.Attr("method")),
			Fields: make(map[string]string),
		}

		// 默认方法为GET
		if form.Method == "" {
			form.Method = "GET"
		}

		// 收集表单字段
		e.ForEach("input", func(i int, input *colly.HTMLElement) {
			name := input.Attr("name")
			inputType := input.Attr("type")
			if name != "" {
				form.Fields[name] = inputType
			}
		})

		e.ForEach("textarea", func(i int, textarea *colly.HTMLElement) {
			name := textarea.Attr("name")
			if name != "" {
				form.Fields[name] = "textarea"
			}
		})

		e.ForEach("select", func(i int, selectElem *colly.HTMLElement) {
			name := selectElem.Attr("name")
			if name != "" {
				form.Fields[name] = "select"
			}
		})

		result.Forms = append(result.Forms, form)
		// 实时输出发现的表单
		logger.Infof("[colly] 发现表单: %s %s (%d个字段)", form.Method, form.Action, len(form.Fields))
	})

	// 收集JavaScript文件（页面中直接引用的）
	c.OnHTML("script[src]", func(e *colly.HTMLElement) {
		src := e.Attr("src")
		absoluteURL := e.Request.AbsoluteURL(src)
		if absoluteURL != "" && strings.HasSuffix(absoluteURL, ".js") {
			// 检查是否已经存在，避免重复
			exists := false
			for _, existing := range result.JSFiles {
				if existing == absoluteURL {
					exists = true
					break
				}
			}
			if !exists {
				result.JSFiles = append(result.JSFiles, absoluteURL)
				logger.Infof("[colly] 发现JS文件: %s", absoluteURL)
			}
		}
	})

	// 收集URL参数
	c.OnHTML("a[href]", func(e *colly.HTMLElement) {
		href := e.Attr("href")
		if href != "" {
			if parsedURL, err := url.Parse(href); err == nil {
				for param := range parsedURL.Query() {
					result.Parameters[param] = "string"
				}
			}
		}
	})

	// 收集Cookie
	c.OnResponse(func(r *colly.Response) {
		if setCookies := r.Headers.Get("Set-Cookie"); setCookies != "" {
			parts := strings.Split(setCookies, ";")
			if len(parts) > 0 {
				cookiePart := strings.TrimSpace(parts[0])
				if cookieKV := strings.Split(cookiePart, "="); len(cookieKV) == 2 {
					cookie := interfaces.Cookie{
						Name:   cookieKV[0],
						Value:  cookieKV[1],
						Domain: r.Request.URL.Host,
						Path:   "/",
					}
					result.Cookies = append(result.Cookies, cookie)
				}
			}
		}
	})

	// 错误处理
	c.OnError(func(r *colly.Response, err error) {
		logger.Errorf("❌ Colly爬取错误 %s: %v", r.Request.URL, err)
	})

	// 请求前处理
	c.OnRequest(func(r *colly.Request) {
		userAgent := r.Headers.Get("User-Agent")
		if ce.config.Crawler.Colly.Request.RandomUserAgent && userAgent != "" {
			logger.Debugf("🔍 Colly访问: %s (User-Agent: %s)", r.URL.String(), userAgent)
		} else {
			logger.Debugf("🔍 Colly访问: %s", r.URL.String())
		}
	})

	// 响应处理
	c.OnResponse(func(r *colly.Response) {
		logger.Debugf("✅ Colly响应: %s [%d]", r.Request.URL, r.StatusCode)
	})

	// 设置深度限制
	if ce.config.Crawler.MaxDepth > 0 {
		c.Limit(&colly.LimitRule{
			DomainGlob:  "*",
			Parallelism: ce.config.Crawler.Concurrency,
			Delay:       ce.config.Crawler.Colly.RateLimit.Delay,
		})
	}

	// 开始爬取
	startTime := time.Now()
	err = c.Visit(target)
	if err != nil {
		return nil, fmt.Errorf("Colly爬取失败: %v", err)
	}

	// 等待完成
	c.Wait()

	duration := time.Since(startTime)
	result.Duration = duration
	logger.Infof("✅ Colly爬取完成，耗时: %v，发现 %d 个URL，%d 个表单，%d 个JS文件",
		duration, len(result.URLs), len(result.Forms), len(result.JSFiles))

	return result, nil
}

// SetConfig 设置配置
func (ce *CollyEngine) SetConfig(cfg interface{}) error {
	if c, ok := cfg.(*config.Config); ok {
		ce.config = c
		return nil
	}
	return fmt.Errorf("无效的配置类型")
}

// GetName 获取爬虫名称
func (ce *CollyEngine) GetName() string {
	return ce.name
}

// GetValidURLs 获取请求成功的URL列表
func (ce *CollyEngine) GetValidURLs() []string {
	var validURLs []string
	for url := range ce.validURLs {
		validURLs = append(validURLs, url)
	}
	return validURLs
}

// GetFailedURLs 获取请求失败的URL列表
func (ce *CollyEngine) GetFailedURLs() []string {
	var failedURLs []string
	for url := range ce.failedURLs {
		failedURLs = append(failedURLs, url)
	}
	return failedURLs
}

// IsURLValid 检查URL是否请求成功
func (ce *CollyEngine) IsURLValid(url string) bool {
	return ce.validURLs[url]
}

// isValidURL 验证URL是否有效
func isValidURL(urlStr string) bool {
	if urlStr == "" {
		return false
	}

	// 解析URL
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return false
	}

	// 检查协议
	if parsedURL.Scheme != "http" && parsedURL.Scheme != "https" {
		return false
	}

	// 检查主机
	if parsedURL.Host == "" {
		return false
	}

	// 过滤一些不需要的文件类型
	excludeExtensions := []string{
		".jpg", ".jpeg", ".png", ".gif", ".bmp", ".svg", ".ico",
		".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
		".zip", ".rar", ".tar", ".gz", ".7z",
		".mp3", ".mp4", ".avi", ".mov", ".wmv", ".flv",
		".css", ".woff", ".woff2", ".ttf", ".eot",
	}

	path := strings.ToLower(parsedURL.Path)
	for _, ext := range excludeExtensions {
		if strings.HasSuffix(path, ext) {
			return false
		}
	}

	return true
}

// extractParameters 从URL中提取参数
func extractParameters(urlStr string) map[string]string {
	params := make(map[string]string)
	
	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return params
	}

	for param := range parsedURL.Query() {
		params[param] = "string"
	}

	return params
}

// CollyStats Colly统计信息
type CollyStats struct {
	RequestCount  int           `json:"request_count"`
	ResponseCount int           `json:"response_count"`
	ErrorCount    int           `json:"error_count"`
	Duration      time.Duration `json:"duration"`
}

// GetStats 获取Colly统计信息
func (ce *CollyEngine) GetStats() *CollyStats {
	// 这里可以添加统计信息收集逻辑
	return &CollyStats{
		RequestCount:  0,
		ResponseCount: 0,
		ErrorCount:    0,
		Duration:      0,
	}
}
