package crawler

import (
	"context"
	"fmt"
	"strings"
	"time"

	"Pulsex/internal/config"
	"Pulsex/internal/logger"
	"Pulsex/pkg/interfaces"
)

// MultiEngineResult 多引擎爬虫结果
type MultiEngineResult struct {
	GAUResult       *interfaces.CrawlResult `json:"gau_result"`
	CollyResult     *interfaces.CrawlResult `json:"colly_result"`
	PulseResult     *interfaces.CrawlResult `json:"pulse_result"`
	CrawlerGoResult *interfaces.CrawlResult `json:"crawlergo_result"`
	MergedResult    *interfaces.CrawlResult `json:"merged_result"`
	TotalDuration   time.Duration          `json:"total_duration"`
}

// MultiEngineCrawler 多引擎爬虫
type MultiEngineCrawler struct {
	config         *config.Config
	gauEngine      *GAUEngine
	collyEngine    *CollyEngine
	pulseEngine    *PulseEngine
	crawlergoEngine *CrawlerGoEngine
}

// NewMultiEngineCrawler 创建多引擎爬虫
func NewMultiEngineCrawler(cfg *config.Config) *MultiEngineCrawler {
	return &MultiEngineCrawler{
		config:          cfg,
		gauEngine:       NewGAUEngine(cfg),
		collyEngine:     NewCollyEngine(cfg),
		pulseEngine:     NewPulseEngine(cfg),
		crawlergoEngine: NewCrawlerGoEngine(cfg),
	}
}

// CrawlWithAllEngines 使用所有引擎进行爬取
func (mec *MultiEngineCrawler) CrawlWithAllEngines(ctx context.Context, targetURL string) (*MultiEngineResult, error) {
	logger.Infof("🚀 启动多引擎爬虫，目标: %s", targetURL)
	startTime := time.Now()

	result := &MultiEngineResult{}

	// 第一阶段：GAU历史URL收集
	logger.Infof("📚 第一阶段：GAU历史URL收集")
	gauResult, err := mec.gauEngine.Crawl(ctx, targetURL)
	if err != nil {
		logger.Warnf("GAU爬取失败: %v", err)
	} else {
		result.GAUResult = gauResult
		logger.Infof("GAU完成，发现 %d 个URL", len(gauResult.URLs))
	}

	// 第二阶段：Colly实时爬取（用于URL有效性验证）
	logger.Infof("🕷️ 第二阶段：Colly实时爬取（验证URL有效性）")
	collyResult, err := mec.collyEngine.Crawl(ctx, targetURL)
	if err != nil {
		logger.Warnf("Colly爬取失败: %v", err)
	} else {
		result.CollyResult = collyResult
		logger.Infof("Colly完成，发现 %d 个URL", len(collyResult.URLs))
	}

	// 获取Colly验证过的有效URL
	validURLs := mec.collyEngine.GetValidURLs()
	logger.Infof("🔍 Colly验证了 %d 个有效URL", len(validURLs))

	// 合并GAU和Colly的结果，但只保留有效的URL
	seedURLs := mec.mergeAndFilterValidURLs(result.GAUResult, result.CollyResult, validURLs)
	logger.Infof("🔗 合并去重并过滤后种子URL数量: %d", len(seedURLs))

	// 第三阶段：Pulse深度爬取
	if len(seedURLs) > 0 {
		logger.Infof("🕸️ 第三阶段：Pulse深度爬取")
		pulseResult, err := mec.pulseEngine.CrawlWithSeeds(ctx, seedURLs)
		if err != nil {
			logger.Warnf("Pulse爬取失败: %v", err)
		} else {
			result.PulseResult = pulseResult
			logger.Infof("Pulse完成，发现 %d 个URL", len(pulseResult.URLs))
		}
	} else {
		logger.Warnf("没有种子URL，跳过Pulse深度爬取")
	}

	// 第四阶段：CrawlerGo JavaScript渲染爬取（只处理需要JS渲染的URL）
	if len(seedURLs) > 0 && mec.config.Crawler.CrawlerGo.Enable {
		logger.Infof("🌐 第四阶段：CrawlerGo JavaScript渲染爬取")
		// 选择需要JavaScript渲染的URL
		jsRenderURLs := mec.selectJavaScriptRenderURLs(seedURLs, validURLs)
		if len(jsRenderURLs) > 0 {
			logger.Infof("🎯 选择了 %d 个需要JavaScript渲染的URL", len(jsRenderURLs))
			crawlergoResult, err := mec.crawlergoEngine.CrawlWithSeeds(ctx, jsRenderURLs)
			if err != nil {
				logger.Warnf("CrawlerGo爬取失败: %v", err)
			} else {
				result.CrawlerGoResult = crawlergoResult
				logger.Infof("CrawlerGo完成，发现 %d 个URL", len(crawlergoResult.URLs))
			}
		} else {
			logger.Infof("🌐 没有需要JavaScript渲染的URL，跳过CrawlerGo")
		}
	} else if !mec.config.Crawler.CrawlerGo.Enable {
		logger.Infof("🌐 CrawlerGo引擎已禁用，跳过JavaScript渲染爬取")
	} else {
		logger.Warnf("没有种子URL，跳过CrawlerGo JavaScript渲染爬取")
	}

	// 合并所有结果
	result.MergedResult = mec.mergeAllResults(result.GAUResult, result.CollyResult, result.PulseResult, result.CrawlerGoResult)
	result.TotalDuration = time.Since(startTime)

	logger.Infof("✅ 多引擎爬虫完成，总耗时: %v", result.TotalDuration)
	logger.Infof("📊 最终统计: %d URLs, %d JS文件, %d 参数, %d 表单", 
		len(result.MergedResult.URLs), 
		len(result.MergedResult.JSFiles), 
		len(result.MergedResult.Parameters), 
		len(result.MergedResult.Forms))

	return result, nil
}

// mergeAndFilterValidURLs 合并并过滤有效的URL
func (mec *MultiEngineCrawler) mergeAndFilterValidURLs(gauResult, collyResult *interfaces.CrawlResult, validURLs []string) []string {
	urlSet := make(map[string]bool)
	validURLSet := make(map[string]bool)
	var allURLs []string

	// 创建有效URL的映射
	for _, url := range validURLs {
		validURLSet[mec.normalizeURL(url)] = true
	}

	// 处理GAU结果
	if gauResult != nil {
		for _, url := range gauResult.URLs {
			normalized := mec.normalizeURL(url)
			if normalized != "" && !urlSet[normalized] {
				// 只有在Colly验证过的URL才加入种子列表
				if validURLSet[normalized] {
					urlSet[normalized] = true
					allURLs = append(allURLs, normalized)
					logger.Debugf("✅ GAU URL验证通过: %s", normalized)
				} else {
					logger.Debugf("❌ GAU URL验证失败，跳过: %s", normalized)
				}
			}
		}
	}

	// 处理Colly结果（这些都是已验证的）
	if collyResult != nil {
		for _, url := range collyResult.URLs {
			normalized := mec.normalizeURL(url)
			if normalized != "" && !urlSet[normalized] {
				urlSet[normalized] = true
				allURLs = append(allURLs, normalized)
			}
		}
	}

	logger.Infof("🔍 URL过滤结果: GAU提供 %d 个URL，验证通过 %d 个",
		len(gauResult.URLs), len(allURLs))

	return allURLs
}

// selectJavaScriptRenderURLs 选择需要JavaScript渲染的URL
func (mec *MultiEngineCrawler) selectJavaScriptRenderURLs(seedURLs []string, validURLs []string) []string {
	var jsRenderURLs []string
	validURLSet := make(map[string]bool)

	// 创建有效URL的映射
	for _, url := range validURLs {
		validURLSet[mec.normalizeURL(url)] = true
	}

	for _, url := range seedURLs {
		normalized := mec.normalizeURL(url)

		// 只处理有效的URL
		if !validURLSet[normalized] {
			logger.Debugf("❌ 跳过无效URL: %s", url)
			continue
		}

		// 检查是否需要JavaScript渲染
		if mec.needsJavaScriptRendering(url) {
			jsRenderURLs = append(jsRenderURLs, url)
			logger.Debugf("🌐 需要JS渲染: %s", url)
		} else {
			logger.Debugf("🚫 不需要JS渲染: %s", url)
		}
	}

	// 限制数量以避免过度消耗资源
	maxJSRenderURLs := 10
	if len(jsRenderURLs) > maxJSRenderURLs {
		logger.Infof("🎯 限制JavaScript渲染URL数量: %d -> %d", len(jsRenderURLs), maxJSRenderURLs)
		jsRenderURLs = jsRenderURLs[:maxJSRenderURLs]
	}

	return jsRenderURLs
}

// needsJavaScriptRendering 判断URL是否需要JavaScript渲染
func (mec *MultiEngineCrawler) needsJavaScriptRendering(url string) bool {
	urlLower := strings.ToLower(url)

	// 检查是否包含JavaScript相关的路径或参数
	jsIndicators := []string{
		"/api/", "/ajax/", "/spa/", "/app/", "/dashboard/",
		"/admin/", "/panel/", "/console/", "/management/",
		"#", // 包含锚点的URL通常是SPA
		"callback=", "jsonp=", "_=", // JSONP相关参数
		"async=", "xhr=", "fetch=", // 异步请求参数
	}

	for _, indicator := range jsIndicators {
		if strings.Contains(urlLower, indicator) {
			return true
		}
	}

	// 检查是否为常见的需要JS渲染的页面类型
	jsPages := []string{
		"login", "signin", "signup", "register", "dashboard",
		"admin", "panel", "console", "management", "settings",
		"profile", "account", "user", "search", "filter",
	}

	for _, page := range jsPages {
		if strings.Contains(urlLower, page) {
			return true
		}
	}

	return false
}

// mergeAndDeduplicateURLs 合并并去重URL
func (mec *MultiEngineCrawler) mergeAndDeduplicateURLs(results ...*interfaces.CrawlResult) []string {
	urlSet := make(map[string]bool)
	var allURLs []string

	for _, result := range results {
		if result == nil {
			continue
		}
		
		// 添加普通URL
		for _, url := range result.URLs {
			normalized := mec.normalizeURL(url)
			if normalized != "" && !urlSet[normalized] {
				urlSet[normalized] = true
				allURLs = append(allURLs, normalized)
			}
		}
		
		// 添加JS文件URL
		for _, jsFile := range result.JSFiles {
			normalized := mec.normalizeURL(jsFile)
			if normalized != "" && !urlSet[normalized] {
				urlSet[normalized] = true
				allURLs = append(allURLs, normalized)
			}
		}
	}

	return allURLs
}

// mergeAllResults 合并所有引擎的结果
func (mec *MultiEngineCrawler) mergeAllResults(results ...*interfaces.CrawlResult) *interfaces.CrawlResult {
	merged := &interfaces.CrawlResult{
		URLs:       make([]string, 0),
		JSFiles:    make([]string, 0),
		Parameters: make(map[string]string),
		Forms:      make([]interfaces.Form, 0),
		Cookies:    make([]interfaces.Cookie, 0),
		Duration:   0,
		Source:     "MultiEngine",
	}

	urlSet := make(map[string]bool)
	jsFileSet := make(map[string]bool)
	formSet := make(map[string]bool)
	cookieSet := make(map[string]bool)

	var totalDuration time.Duration

	for _, result := range results {
		if result == nil {
			continue
		}

		totalDuration += result.Duration

		// 合并URL
		for _, url := range result.URLs {
			normalized := mec.normalizeURL(url)
			if normalized != "" && !urlSet[normalized] {
				urlSet[normalized] = true
				merged.URLs = append(merged.URLs, normalized)
			}
		}

		// 合并JS文件
		for _, jsFile := range result.JSFiles {
			normalized := mec.normalizeURL(jsFile)
			if normalized != "" && !jsFileSet[normalized] {
				jsFileSet[normalized] = true
				merged.JSFiles = append(merged.JSFiles, normalized)
			}
		}

		// 合并参数
		for param, paramType := range result.Parameters {
			if param != "" {
				merged.Parameters[param] = paramType
			}
		}

		// 合并表单
		for _, form := range result.Forms {
			formKey := fmt.Sprintf("%s:%s", form.Method, form.Action)
			if !formSet[formKey] {
				formSet[formKey] = true
				merged.Forms = append(merged.Forms, form)
			}
		}

		// 合并Cookie
		for _, cookie := range result.Cookies {
			cookieKey := fmt.Sprintf("%s:%s", cookie.Name, cookie.Domain)
			if !cookieSet[cookieKey] {
				cookieSet[cookieKey] = true
				merged.Cookies = append(merged.Cookies, cookie)
			}
		}
	}

	merged.Duration = totalDuration
	return merged
}

// selectHighValueURLs 选择高价值URL进行JavaScript渲染
func (mec *MultiEngineCrawler) selectHighValueURLs(urls []string, maxCount int) []string {
	if len(urls) == 0 {
		return []string{}
	}

	// URL价值评分
	type urlScore struct {
		url   string
		score int
	}

	var scored []urlScore

	for _, url := range urls {
		score := mec.calculateURLValue(url)
		scored = append(scored, urlScore{url: url, score: score})
	}

	// 按分数排序
	for i := 0; i < len(scored)-1; i++ {
		for j := i + 1; j < len(scored); j++ {
			if scored[i].score < scored[j].score {
				scored[i], scored[j] = scored[j], scored[i]
			}
		}
	}

	// 选择前maxCount个高价值URL
	var selected []string
	count := maxCount
	if len(scored) < count {
		count = len(scored)
	}

	for i := 0; i < count; i++ {
		selected = append(selected, scored[i].url)
		logger.Debugf("选择高价值URL (分数:%d): %s", scored[i].score, scored[i].url)
	}

	logger.Infof("从 %d 个URL中选择了 %d 个高价值URL进行JavaScript渲染", len(urls), len(selected))
	return selected
}

// calculateURLValue 计算URL价值分数
func (mec *MultiEngineCrawler) calculateURLValue(url string) int {
	score := 0
	lowerURL := strings.ToLower(url)

	// 高价值路径模式
	highValuePatterns := map[string]int{
		"/admin":     100,
		"/login":     90,
		"/auth":      90,
		"/api/":      80,
		"/dashboard": 85,
		"/panel":     80,
		"/console":   85,
		"/upload":    75,
		"/download":  70,
		"/backup":    90,
		"/config":    95,
		"/settings":  75,
		"/debug":     100,
		"/test":      60,
		"/dev":       70,
		"/internal":  85,
		"/private":   90,
	}

	// 计算模式分数
	for pattern, points := range highValuePatterns {
		if strings.Contains(lowerURL, pattern) {
			score += points
		}
	}

	// 参数加分
	if strings.Contains(url, "?") {
		score += 30
	}

	// 动态页面扩展名加分
	dynamicExtensions := []string{".php", ".asp", ".aspx", ".jsp", ".do", ".action"}
	for _, ext := range dynamicExtensions {
		if strings.HasSuffix(lowerURL, ext) {
			score += 40
			break
		}
	}

	return score
}

// normalizeURL 标准化URL
func (mec *MultiEngineCrawler) normalizeURL(rawURL string) string {
	if rawURL == "" {
		return ""
	}

	// 移除空白字符
	rawURL = strings.TrimSpace(rawURL)
	
	// 确保URL有协议
	if !strings.HasPrefix(rawURL, "http://") && !strings.HasPrefix(rawURL, "https://") {
		rawURL = "http://" + rawURL
	}

	// 简单的URL清理
	// 移除fragment
	if idx := strings.Index(rawURL, "#"); idx != -1 {
		rawURL = rawURL[:idx]
	}

	return rawURL
}

// GetEngineStats 获取各引擎统计信息
func (mec *MultiEngineCrawler) GetEngineStats(result *MultiEngineResult) map[string]interface{} {
	stats := make(map[string]interface{})

	if result.GAUResult != nil {
		stats["gau"] = map[string]interface{}{
			"urls":       len(result.GAUResult.URLs),
			"js_files":   len(result.GAUResult.JSFiles),
			"parameters": len(result.GAUResult.Parameters),
			"forms":      len(result.GAUResult.Forms),
			"cookies":    len(result.GAUResult.Cookies),
			"duration":   result.GAUResult.Duration.String(),
			"source":     result.GAUResult.Source,
		}
	}

	if result.CollyResult != nil {
		stats["colly"] = map[string]interface{}{
			"urls":       len(result.CollyResult.URLs),
			"js_files":   len(result.CollyResult.JSFiles),
			"parameters": len(result.CollyResult.Parameters),
			"forms":      len(result.CollyResult.Forms),
			"cookies":    len(result.CollyResult.Cookies),
			"duration":   result.CollyResult.Duration.String(),
			"source":     result.CollyResult.Source,
		}
	}

	if result.PulseResult != nil {
		stats["pulse"] = map[string]interface{}{
			"urls":       len(result.PulseResult.URLs),
			"js_files":   len(result.PulseResult.JSFiles),
			"parameters": len(result.PulseResult.Parameters),
			"forms":      len(result.PulseResult.Forms),
			"cookies":    len(result.PulseResult.Cookies),
			"duration":   result.PulseResult.Duration.String(),
			"source":     result.PulseResult.Source,
		}
	}

	if result.MergedResult != nil {
		stats["merged"] = map[string]interface{}{
			"urls":       len(result.MergedResult.URLs),
			"js_files":   len(result.MergedResult.JSFiles),
			"parameters": len(result.MergedResult.Parameters),
			"forms":      len(result.MergedResult.Forms),
			"cookies":    len(result.MergedResult.Cookies),
			"duration":   result.MergedResult.Duration.String(),
			"source":     result.MergedResult.Source,
		}
	}

	stats["total_duration"] = result.TotalDuration.String()

	return stats
}
