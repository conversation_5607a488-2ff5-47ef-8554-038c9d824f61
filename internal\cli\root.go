package cli

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/fatih/color"
	"Pulsex/internal/config"
	"Pulsex/internal/logger"
)

const (
	Version = "1.0.0"
	AppName = "Pulsex"
)

var (
	cfgFile    string
	debug      bool
	outputDir  string
	verbose    bool
	quiet      bool
	configData *config.Config

	// 颜色定义
	red    = color.New(color.FgRed).SprintFunc()
	green  = color.New(color.FgGreen).SprintFunc()
	yellow = color.New(color.FgYellow).SprintFunc()
	blue   = color.New(color.FgBlue).SprintFunc()
	purple = color.New(color.FgMagenta).SprintFunc()
	cyan   = color.New(color.FgCyan).SprintFunc()
	white  = color.New(color.FgWhite).SprintFunc()

	// 带背景色
	greenBg = color.New(color.FgWhite, color.BgGreen).SprintFunc()
	redBg   = color.New(color.FgWhite, color.BgRed).SprintFunc()
	blueBg  = color.New(color.FgWhite, color.BgBlue).SprintFunc()
)

// printBanner 打印应用横幅
func printBanner() {
	if quiet {
		return
	}
	banner := fmt.Sprintf(`
    ____        __
   / __ \__  __/ /___ ___  _  __
  / /_/ / / / / / __ `+"`"+`__ \| |/_/
 / ____/ /_/ / / /_/ / / //>  <
/_/    \__,_/_/\__,_/_/ /_/_/|_|

🚀 %s v%s - 专业XSS漏洞扫描框架 🚀
`, AppName, Version)
	fmt.Println(cyan(banner))
}

// rootCmd 根命令
var rootCmd = &cobra.Command{
	Use:   "pulsex",
	Short: "🔍 Pulsex - 专业XSS漏洞扫描框架",
	Long: cyan(fmt.Sprintf(`
    ____        __
   / __ \__  __/ /___ ___  _  __
  / /_/ / / / / / __ `+"`"+`__ \| |/_/
 / ____/ /_/ / / /_/ / / //>  <
/_/    \__,_/_/\__,_/_/ /_/_/|_|

🚀 %s v%s - 专业XSS漏洞扫描框架 🚀

一款基于 Go 语言开发的高性能、全自动 XSS 漏洞扫描框架。
集成了多种爬虫技术、AST 语法验证、智能探测等先进技术。

核心功能模块:
  🕷️  多引擎爬虫系统 (Colly, Pulse, GAU, CrawlerGo)
  🔍 智能XSS检测引擎 (AST语法验证 + 上下文感知)
  📁 目录遍历扫描 (智能字典 + 状态码分析)
  📜 JavaScript深度分析 (DOM-XSS + 反射型检测)
  🔄 被动扫描代理 (流量拦截 + 实时分析)
  📊 专业报告生成 (HTML + JSON + PDF)

使用示例:
  pulsex crawl -u https://example.com        # 单目标扫描
  pulsex crawl -f targets.txt               # 批量目标扫描
  pulsex crawl -u target.com -e all         # 使用所有引擎
  pulsex proxy -p 8080                      # 启动被动代理
  pulsex version                            # 查看版本信息

更多帮助: pulsex [command] --help`, AppName, Version)),
	PersistentPreRunE: func(cmd *cobra.Command, args []string) error {
		// 只在非help命令时显示banner
		if cmd.Name() != "help" && cmd.Name() != "completion" && cmd.Name() != "version" {
			printBanner()
		}

		// 版本命令不需要配置文件
		if cmd.Name() == "version" {
			return nil
		}

		// 初始化配置
		var err error
		configData, err = config.LoadConfig(cfgFile)
		if err != nil {
			return fmt.Errorf("加载配置失败: %v", err)
		}

		// 覆盖配置中的调试和输出目录设置
		if debug {
			configData.Debug = true
		}
		if outputDir != "" {
			configData.OutputDir = outputDir
		}

		// 初始化日志系统
		logLevel := "info"
		if configData.Debug {
			logLevel = "debug"
		}
		if verbose {
			logLevel = "debug"
		}
		if quiet {
			logLevel = "error"
		}

		err = logger.InitLogger(logLevel, configData.OutputDir, configData.Debug)
		if err != nil {
			return fmt.Errorf("初始化日志系统失败: %v", err)
		}

		return nil
	},
	SilenceUsage:  true,
	SilenceErrors: true,
}

// Execute 执行根命令
func Execute() error {
	if err := rootCmd.Execute(); err != nil {
		fmt.Printf("%s %s\n", redBg(" 错误 "), err.Error())
		os.Exit(1)
	}
	return nil
}

func init() {
	// 全局标志
	rootCmd.PersistentFlags().StringVarP(&cfgFile, "config", "c", "config/config.yaml", "📁 配置文件路径")
	rootCmd.PersistentFlags().BoolVarP(&debug, "debug", "d", false, "🐛 启用调试模式")
	rootCmd.PersistentFlags().StringVarP(&outputDir, "output", "o", "", "📂 输出目录")
	rootCmd.PersistentFlags().BoolVarP(&verbose, "verbose", "v", false, "🔍 详细输出模式")
	rootCmd.PersistentFlags().BoolVarP(&quiet, "quiet", "q", false, "🔇 静默模式")

	// 添加子命令
	rootCmd.AddCommand(crawlCmd)
	rootCmd.AddCommand(xssCmd)
	rootCmd.AddCommand(dirCmd)
	rootCmd.AddCommand(jsCmd)
	rootCmd.AddCommand(proxyCmd)
	rootCmd.AddCommand(portCmd)
	rootCmd.AddCommand(versionCmd)
	rootCmd.AddCommand(completionCmd)
}

// getConfig 获取配置
func getConfig() *config.Config {
	return configData
}

// printModuleStatus 打印模块状态
func printModuleStatus() {
	if configData == nil || quiet {
		return
	}

	fmt.Printf("%s 配置文件加载成功\n", greenBg(" 成功 "))
	logger.PrintModuleStatus("Colly", configData.Crawler.Colly.Enable)
	logger.PrintModuleStatus("Pulse", configData.Crawler.Pulse.Enable)
	logger.PrintModuleStatus("GAU", configData.Crawler.GAU.Enable)
	logger.PrintModuleStatus("CrawlerGo", configData.Crawler.CrawlerGo.Enable)
	logger.PrintModuleStatus("XSS-Scan", configData.XSS.Enable)
	logger.PrintModuleStatus("DirScan", configData.DirScan.Enable)
	logger.PrintModuleStatus("JS-Analyzer", configData.JSAnalyzer.Enable)
	fmt.Println(cyan("-----------------------------------------------------------"))
}
