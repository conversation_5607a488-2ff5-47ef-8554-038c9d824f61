---
name: CI
on:
  push:
    branches:
      - '**'
  pull_request:

jobs:
  test:
    name: Test ${{matrix.go}}
    runs-on: [ubuntu-latest]
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        go: [
          "1.24",
          "1.23",
          "1.22",
          "1.21",
        ]

    steps:
      - name: Checkout branch
        uses: actions/checkout@v2

      - name: Setup go
        uses: actions/setup-go@v2
        with:
          go-version: ${{matrix.go}}

      - name: Test
        run: |
          go install golang.org/x/lint/golint@latest
          OUT="$(go get -a)"; test -z "$OUT" || (echo "$OUT" && return 1)
          OUT="$(gofmt -l -d ./)"; test -z "$OUT" || (echo "$OUT" && return 1)
          golint -set_exit_status
          go vet -v ./...
          go test -race -v -coverprofile=coverage.txt -covermode=atomic ./...

  build:
    name: Build ${{matrix.go}}
    runs-on: [ubuntu-latest]
    strategy:
      fail-fast: false
      max-parallel: 4
      matrix:
        go: [
          "1.24",
          "1.23",
          "1.22",
          "1.21",
        ]

    steps:
      - name: Checkout branch
        uses: actions/checkout@v2

      - name: Setup go
        uses: actions/setup-go@v2
        with:
          go-version: ${{matrix.go}}

      - name: Build
        run: |
          go install golang.org/x/lint/golint@latest
          OUT="$(go get -a)"; test -z "$OUT" || (echo "$OUT" && return 1)
          OUT="$(gofmt -l -d ./)"; test -z "$OUT" || (echo "$OUT" && return 1)
          golint -set_exit_status
          go build

  codecov:
    name: Codecov 
    runs-on: [ubuntu-latest]
    needs: 
      - test
      - build
    steps:
      - name: Run Codecov 
        run: bash <(curl -s https://codecov.io/bash)
