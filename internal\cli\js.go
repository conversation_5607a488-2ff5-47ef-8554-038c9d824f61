package cli

import (
	"context"
	"fmt"

	"github.com/spf13/cobra"
	"Pulsex/internal/logger"
)

var (
	jsURL     string
	jsFile    string
	jsThreads int
)

// jsCmd 执行JS分析任务
var jsCmd = &cobra.Command{
	Use:   "js",
	Short: "执行JS分析任务",
	Long: `执行JavaScript深度分析任务

专门用于分析JavaScript文件，提取敏感信息和API端点。
支持多种JavaScript分析技术。

核心特性:
  • 敏感信息提取: 自动识别API密钥、Token、密码等敏感信息
  • API端点发现: 提取JavaScript中的API接口地址
  • 未授权检测: 自动测试发现的API端点是否存在未授权访问
  • 变量分析: 分析JavaScript变量和函数
  • 正则匹配: 使用多种正则表达式模式匹配敏感内容

检测内容:
  • API Keys (AWS, Google, GitHub等)
  • Access Tokens
  • 数据库连接字符串
  • 内部API端点
  • 敏感URL路径
  • 配置信息

示例:
  pulsex js -u https://example.com/app.js
  pulsex js -f js_files.txt
  pulsex js -u https://example.com/static/main.js -t 5`,
	RunE: func(cmd *cobra.Command, args []string) error {
		config := getConfig()
		if config == nil {
			return fmt.Errorf("配置未初始化")
		}

		// 检查JS分析模块是否启用
		if !config.JSAnalyzer.Enable {
			return fmt.Errorf("JavaScript分析模块未启用，请检查配置文件")
		}

		// 验证参数
		if jsURL == "" && jsFile == "" {
			return fmt.Errorf("必须指定 -u (URL) 或 -f (文件)")
		}

		if jsURL != "" && jsFile != "" {
			return fmt.Errorf("不能同时指定 -u 和 -f 参数")
		}

		// 打印模块状态
		logger.PrintModuleStatus("JS", config.JSAnalyzer.Enable)
		fmt.Printf("[%s] [%s] 敏感信息检测: %s\n", 
			logger.Blue("JS-ANALYZER"), 
			logger.Green("INFO"), 
			getBoolStatus(config.JSAnalyzer.SensitiveInfo))
		fmt.Printf("[%s] [%s] API端点发现: %s\n", 
			logger.Blue("JS-ANALYZER"), 
			logger.Green("INFO"), 
			getBoolStatus(config.JSAnalyzer.APIEndpoints))
		fmt.Printf("[%s] [%s] 未授权检测: %s\n", 
			logger.Blue("JS-ANALYZER"), 
			logger.Green("INFO"), 
			getBoolStatus(config.JSAnalyzer.UnauthorizedTest))
		fmt.Println("-----------------------------------------------------------")

		// 获取目标列表
		var targets []string
		if jsURL != "" {
			targets = []string{jsURL}
		} else {
			var err error
			targets, err = readTargetsFromFile(jsFile)
			if err != nil {
				return fmt.Errorf("读取目标文件失败: %v", err)
			}
		}

		if len(targets) == 0 {
			return fmt.Errorf("没有找到有效的目标")
		}

		logger.Infof("开始JavaScript分析，目标数量: %d", len(targets))

		// 创建上下文
		ctx := context.Background()

		// 执行JS分析
		totalFindings := 0
		for i, target := range targets {
			logger.PrintProgress(i+1, len(targets), fmt.Sprintf("JS分析: %s", target))
			
			findings, err := analyzeJSTarget(ctx, target, config)
			if err != nil {
				logger.Errorf("分析目标 %s 失败: %v", target, err)
				continue
			}
			
			totalFindings += findings
		}

		if totalFindings > 0 {
			logger.Success(fmt.Sprintf("JavaScript分析完成，发现 %d 个结果", totalFindings))
		} else {
			logger.Success("JavaScript分析完成，未发现敏感信息")
		}

		return nil
	},
}

func init() {
	jsCmd.Flags().StringVarP(&jsURL, "url", "u", "", "JavaScript文件URL")
	jsCmd.Flags().StringVarP(&jsFile, "file", "f", "", "目标文件路径")
	jsCmd.Flags().IntVarP(&jsThreads, "threads", "t", 10, "并发线程数")
}

// analyzeJSTarget 分析单个JavaScript目标
func analyzeJSTarget(ctx context.Context, target string, config interface{}) (int, error) {
	logger.Infof("开始JavaScript分析: %s", target)
	
	// TODO: 实现具体的JavaScript分析逻辑
	// 1. 下载JavaScript文件内容
	// 2. 解析JavaScript代码
	// 3. 提取敏感信息
	//    - API密钥模式匹配
	//    - Token模式匹配
	//    - 数据库连接字符串
	//    - 内部URL和端点
	// 4. 发现API端点
	//    - 提取fetch/axios请求
	//    - 提取XMLHttpRequest
	//    - 提取WebSocket连接
	// 5. 未授权检测
	//    - 测试发现的API端点
	//    - 发送GET/POST请求
	//    - 分析响应判断是否存在未授权
	// 6. 生成分析报告
	
	findingsCount := 0
	
	// 模拟分析过程
	if target != "" {
		// 这里应该是实际的JavaScript分析逻辑
		logger.Infof("JavaScript分析完成: %s", target)
	}
	
	return findingsCount, nil
}

// getBoolStatus 获取布尔值状态字符串
func getBoolStatus(enabled bool) string {
	if enabled {
		return logger.Green("启用")
	}
	return logger.Red("禁用")
}
